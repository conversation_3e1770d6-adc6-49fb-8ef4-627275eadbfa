# How to Generate latest.yml with Real File Data

## Problem
You need to get the real **file sizes** and **SHA512 hashes** from your CoreDesk executables to create an accurate `latest.yml` file.

## Solution: Automated Script

I've created `generate-latest-yml.js` that automatically:
1. ✅ **Reads file sizes** from actual executables
2. ✅ **Calculates SHA512 hashes** for integrity verification
3. ✅ **Generates latest.yml** with real data
4. ✅ **Provides deployment instructions**

## How to Use

### Step 1: Prepare Your Executables
Place your CoreDesk executables in a directory with these exact names:
```
CoreDesk-0.0.2-win.exe      # Windows executable
CoreDesk-0.0.2-mac.zip      # macOS package  
CoreDesk-0.0.2-linux.AppImage  # Linux AppImage
```

### Step 2: Run the Generation Script
```bash
# Copy the script to your executables directory
cp generate-latest-yml.js /path/to/your/executables/

# Navigate to the directory
cd /path/to/your/executables/

# Run the script
node generate-latest-yml.js
```

### Step 3: Example Output
```
🔧 Generating latest.yml with real file data...

📁 Processing CoreDesk-0.0.2-win.exe...
   📏 Size: ~88 MB (92212002 bytes)
   🔒 Calculating SHA512 hash...
   ✅ SHA512: 47784dbdf0e6ba4d76a9...

📁 Processing CoreDesk-0.0.2-mac.zip...
   📏 Size: ~121 MB (126391687 bytes)
   🔒 Calculating SHA512 hash...
   ✅ SHA512: 6b14a62b6e390fb0bc9e...

📁 Processing CoreDesk-0.0.2-linux.AppImage...
   📏 Size: ~124 MB (129319507 bytes)
   🔒 Calculating SHA512 hash...
   ✅ SHA512: 0f3b890acee85cabb708...

✅ Generated latest.yml:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
version: 0.0.2
releaseDate: 2025-07-07T03:45:00.000Z
vwindows:
  url: CoreDesk-0.0.2-win.exe
  sha512: 47784dbdf0e6ba4d76a926062f2dff208cedf9f5883db970e54321b88622cbfc0d0cb1626da0ab005346b7fa926674ba2fcfc25dbbde3ed155670133a2248ba2
  size: 92212002
vmacos:
  url: CoreDesk-0.0.2-mac.zip
  sha512: 6b14a62b6e390fb0bc9e2a45201e5b4f140421ce916fc647b9d7eb3945b3ae4a099d9e6fa9928009ffeb44bdd53fcda3aad27e1dc545e56f929e78a17e47bedd
  size: 126391687
vlinux:
  url: CoreDesk-0.0.2-linux.AppImage
  sha512: 0f3b890acee85cabb7080633dde677bd37349f3becd5aa7261b7296ce90ea497af8cb4d4a703ee213a9b1c474cc8f1d5166539e8a22e365fd1d00ba84b42f950
  size: 129319507
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

## Manual Method (Alternative)

If you prefer to do it manually:

### Get File Size
```bash
# On Linux/macOS
ls -la CoreDesk-0.0.2-win.exe
# Look at the 5th column for size in bytes

# On Windows
dir CoreDesk-0.0.2-win.exe
# Look at the size in bytes
```

### Calculate SHA512 Hash
```bash
# On Linux/macOS
sha512sum CoreDesk-0.0.2-win.exe

# On Windows (PowerShell)
Get-FileHash -Algorithm SHA512 CoreDesk-0.0.2-win.exe

# On Windows (CertUtil)
certutil -hashfile CoreDesk-0.0.2-win.exe SHA512
```

### Create YAML Manually
```yaml
version: 0.0.2
releaseDate: 2025-07-07T00:00:00.000Z
vwindows:
  url: CoreDesk-0.0.2-win.exe
  sha512: [paste the hash here]
  size: [paste the size in bytes here]
# ... repeat for other platforms
```

## Deployment to Production

After generating `latest.yml`:

```bash
# 1. Upload latest.yml
scp latest.yml administrator@**************:/opt/coredesk/data/downloads/app/

# 2. Upload executables (if not already uploaded)
scp CoreDesk-0.0.2-win.exe administrator@**************:/opt/coredesk/data/downloads/app/
scp CoreDesk-0.0.2-mac.zip administrator@**************:/opt/coredesk/data/downloads/app/
scp CoreDesk-0.0.2-linux.AppImage administrator@**************:/opt/coredesk/data/downloads/app/

# 3. Restart portal container (or wait 5 minutes for cache refresh)
ssh administrator@************** "cd /opt/coredesk && docker-compose restart portal"
```

## Verification

After deployment:
1. **Visit download page**: Should show real file sizes
2. **Check logs**: Should see "Using dynamic version info" 
3. **Test downloads**: Verify files download correctly

## For Future Versions

1. **Build new executables** (e.g., v0.0.3)
2. **Update filenames** in the script or rename files
3. **Run script again** to generate new `latest.yml`
4. **Deploy** following the same process

The script makes this process **repeatable and error-free** for future releases!