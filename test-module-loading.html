<!DOCTYPE html>
<html>
<head>
    <title>Test Module Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .info { background-color: #d1ecf1; }
        .loading { background-color: #fff3cd; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Module Loading Test</h1>
    <div id="test-results"></div>
    <button onclick="runModuleTest()">Run Module Test</button>

    <script>
        // Mock CoreDeskAuth
        window.CoreDeskAuth = {
            environment: 'production'
        };

        // Mock ModuleDownloader
        class ModuleDownloader {
            constructor(options = {}) {
                this.config = {
                    baseUrl: this.getDefaultBaseUrl(),
                    ...options
                };
            }

            getDefaultBaseUrl() {
                const environment = window.CoreDeskAuth?.environment || 'production';
                if (environment === 'development') {
                    return 'http://localhost:3000/api/modules';
                } else {
                    return 'https://coredeskpro.com/api/modules';
                }
            }

            async fetchAvailableModules(filters = {}) {
                const url = this.config.baseUrl;
                const queryParams = new URLSearchParams();
                
                Object.entries(filters).forEach(([key, value]) => {
                    if (value) queryParams.append(key, value);
                });
                
                const fullUrl = queryParams.toString() ? `${url}?${queryParams}` : url;
                
                const response = await fetch(fullUrl);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                return data.modules || [];
            }
        }

        // Mock ModuleRegistry
        class ModuleRegistry {
            constructor() {
                this.modules = new Map();
            }

            async initialize() {
                // Mock initialization
            }

            getInstalledModules() {
                return Array.from(this.modules.values());
            }

            isModuleRegistered(moduleId) {
                return this.modules.has(moduleId);
            }
        }

        // Mock DynamicModuleManager
        class DynamicModuleManager {
            constructor() {
                this.registry = null;
                this.downloader = null;
                this.isInitialized = false;
            }

            getModuleRepositoryUrl() {
                const environment = window.CoreDeskAuth?.environment || 'production';
                if (environment === 'development') {
                    return 'http://localhost:3000/api/modules';
                } else {
                    return 'https://coredeskpro.com/api/modules';
                }
            }

            async initialize() {
                this.registry = new ModuleRegistry();
                await this.registry.initialize();
                
                this.downloader = new ModuleDownloader({
                    baseUrl: this.getModuleRepositoryUrl()
                });
                
                this.isInitialized = true;
            }

            async getAvailableModules() {
                return await this.downloader.fetchAvailableModules();
            }

            getInstalledModules() {
                return this.registry.getInstalledModules();
            }

            isModuleActive(moduleId) {
                return false; // Mock - no modules active
            }
        }

        // Make classes available globally
        window.ModuleDownloader = ModuleDownloader;
        window.ModuleRegistry = ModuleRegistry;
        window.DynamicModuleManager = DynamicModuleManager;

        function addResult(type, title, content) {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<h3>${title}</h3><div>${content}</div>`;
            results.appendChild(div);
        }

        async function runModuleTest() {
            document.getElementById('test-results').innerHTML = '';
            
            addResult('info', 'Starting Test', 'Testing the complete module loading flow...');

            try {
                // Step 1: Create DynamicModuleManager
                addResult('loading', 'Step 1', 'Creating DynamicModuleManager...');
                const dynamicManager = new DynamicModuleManager();
                
                // Step 2: Initialize
                addResult('loading', 'Step 2', 'Initializing DynamicModuleManager...');
                await dynamicManager.initialize();
                
                // Step 3: Get available modules
                addResult('loading', 'Step 3', 'Fetching available modules...');
                const availableModules = await dynamicManager.getAvailableModules();
                
                addResult('success', 'Available Modules', `
                    <p>Found ${availableModules.length} modules:</p>
                    <pre>${JSON.stringify(availableModules, null, 2)}</pre>
                `);

                // Step 4: Test module display generation
                addResult('loading', 'Step 4', 'Testing module display generation...');
                const moduleCards = availableModules.map(module => {
                    const isInstalled = dynamicManager.registry.isModuleRegistered(module.id);
                    const isActive = dynamicManager.isModuleActive(module.id);
                    
                    return `
                        <div class="module-card ${isInstalled ? 'available' : 'unavailable'}" data-module="${module.id}">
                            <div class="module-icon">📦</div>
                            <h4>${module.name}</h4>
                            <p>${module.description}</p>
                            <div class="module-status">
                                ${isInstalled ? 
                                    (isActive ? '<span class="status-active">Activo</span>' : '<span class="status-installed">Instalado</span>') :
                                    '<span class="status-coming-soon">Disponible</span>'
                                }
                            </div>
                        </div>
                    `;
                }).join('');

                addResult('success', 'Module Cards Generated', `
                    <p>Generated ${availableModules.length} module cards:</p>
                    <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px;">
                        ${moduleCards}
                    </div>
                `);

                addResult('success', 'Test Complete', 'All tests passed! The module loading system should work correctly.');

            } catch (error) {
                addResult('error', 'Test Failed', `
                    <p>Error: ${error.message}</p>
                    <pre>${error.stack}</pre>
                `);
            }
        }
    </script>
</body>
</html>