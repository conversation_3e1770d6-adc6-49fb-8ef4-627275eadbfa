{"id": "protocolx", "name": "ProtocolX", "version": "0.0.2", "description": "Automatización de procesos administrativos y protocolos digitales", "author": "CoreDesk Team", "license": "Proprietary", "category": "automation", "tags": ["automation", "protocols", "admin", "processes"], "main": "ProtocolXModule.js", "package": "ProtocolXPackage.js", "style": "protocolx.css", "install": "install.js", "dependencies": {"coredesk": ">=0.0.2"}, "requiredPermissions": ["storage.read", "storage.write", "ui.tabs", "ui.panels"], "requiredLicense": "professional", "engines": {"coredesk": ">=0.0.2"}, "homepage": "https://coredeskpro.com/modules/protocolx", "repository": {"type": "git", "url": "https://github.com/coredesk/modules/protocolx"}, "packageFile": "protocolx-0.0.2.tar.gz", "checksum": "sha256:cf7017043bb25bf971668b674c7c29cb5e6aea7c36681b09a34a775bce7d51df", "size": 13824, "downloadCount": 0, "lastUpdated": "2025-07-08T03:41:35.327Z", "compatibility": {"coredesk": ">=0.0.2", "platforms": ["windows", "macos", "linux"]}, "coredesk": {"moduleType": "ui", "activationMethod": "lazy", "supportedThemes": ["dark", "light"], "minScreenWidth": 1024, "moduleIcon": "🔧", "moduleColor": "#059669"}, "files": ["ProtocolXModule.js", "ProtocolXPackage.js", "protocolx.css", "install.js", "package.json"]}