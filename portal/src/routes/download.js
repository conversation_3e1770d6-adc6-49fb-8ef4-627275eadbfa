const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const config = require('../config');
const logger = require('../middleware/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const downloadService = require('../services/downloadService');
const versionService = require('../services/versionService');

const router = express.Router();

// API endpoint to get current version info for frontend
router.get('/api/version', asyncHandler(async (req, res) => {
  try {
    const versionInfo = await versionService.getVersionInfo();
    res.json({
      success: true,
      data: versionInfo
    });
  } catch (error) {
    logger.error('Error getting version info for API:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener información de versión'
    });
  }
}));

// Página principal de descargas
router.get('/', asyncHandler(async (req, res) => {
  logger.info('Acceso a página de descargas');
  
  try {
    // Get dynamic version info for the page
    const versionInfo = await versionService.getVersionInfo();
    const releases = await downloadService.getAvailableReleases();
    const latestRelease = releases[0] || null;
    
    const pageData = {
      title: 'Descargar CoreDesk',
      description: 'Descarga la última versión de CoreDesk para tu sistema operativo',
      latestRelease,
      releases: releases.slice(0, 5), // Solo las últimas 5 versiones
      versionInfo, // Dynamic version info
      supportedPlatforms: [
        {
          name: 'Windows',
          icon: 'fab fa-windows',
          versions: ['Windows 10', 'Windows 11'],
          architecture: ['x64', 'x86'],
          format: '.exe'
        },
        {
          name: 'macOS',
          icon: 'fab fa-apple',
          versions: ['macOS 10.15+'],
          architecture: ['Intel', 'Apple Silicon'],
          format: '.dmg'
        },
        {
          name: 'Linux',
          icon: 'fab fa-linux',
          versions: ['Ubuntu 18.04+', 'CentOS 7+', 'Debian 10+'],
          architecture: ['x64'],
          format: '.AppImage, .deb, .rpm'
        }
      ],
      systemRequirements: {
        minimum: {
          ram: '4 GB',
          storage: '2 GB',
          processor: 'Intel Core i3 / AMD Ryzen 3'
        },
        recommended: {
          ram: '8 GB',
          storage: '5 GB',
          processor: 'Intel Core i5 / AMD Ryzen 5'
        }
      }
    };
    
    res.render('download', pageData);
  } catch (error) {
    logger.error('Error al cargar página de descargas:', error);
    
    // Even on error, try to get fallback version info
    const fallbackVersionInfo = await versionService.getVersionInfo();
    
    res.render('download', {
      title: 'Descargar CoreDesk',
      description: 'Descarga la última versión de CoreDesk',
      versionInfo: fallbackVersionInfo,
      error: 'No se pudieron cargar las versiones disponibles'
    });
  }
}));

// Obtener información de una versión específica
router.get('/version/:version', asyncHandler(async (req, res) => {
  const { version } = req.params;
  
  try {
    const releaseInfo = await downloadService.getReleaseInfo(version);
    
    if (!releaseInfo) {
      return res.status(404).json({
        success: false,
        message: 'Versión no encontrada'
      });
    }
    
    res.json({
      success: true,
      release: releaseInfo
    });
  } catch (error) {
    logger.error(`Error al obtener información de versión ${version}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener información de la versión'
    });
  }
}));

// Descargar última versión para un SO - DYNAMIC VERSION APPROACH
// IMPORTANT: This route must come BEFORE /:platform/:version to avoid conflicts
router.get('/latest/:platform', asyncHandler(async (req, res) => {
  const { platform } = req.params;
  
  logger.info(`DYNAMIC REDIRECT: Latest version request for ${platform}`);
  
  try {
    // Get dynamic version info with fallback to hardcoded values
    const downloadInfo = await versionService.getDownloadUrl(platform);
    
    if (downloadInfo) {
      logger.info(`DYNAMIC REDIRECT: Redirecting ${platform} to ${downloadInfo.url} (v${downloadInfo.version})`);
      
      // Log download attempt
      try {
        await downloadService.logDownload({
          platform: platform.toLowerCase(),
          version: downloadInfo.version,
          architecture: 'x64',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date()
        });
      } catch (error) {
        logger.warn(`Failed to log download: ${error.message}`);
      }
      
      return res.redirect(downloadInfo.url);
    } else {
      logger.error(`DYNAMIC REDIRECT: No URL found for platform ${platform}`);
      return res.status(404).json({
        success: false,
        message: 'Plataforma no soportada'
      });
    }
  } catch (error) {
    logger.error(`DYNAMIC REDIRECT: Error processing ${platform}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error al procesar la descarga'
    });
  }
}));

// Descargar versión específica para un SO - DIRECT REDIRECT APPROACH
// IMPORTANT: This route must come AFTER /latest/:platform to avoid conflicts
router.get('/:platform/:version', asyncHandler(async (req, res) => {
  const { platform, version } = req.params;
  
  logger.info(`DIRECT REDIRECT: Download request: ${platform} v${version}`);
  
  // Direct mapping for specific versions - bypass all services
  const versionUrls = {
    'windows': {
      '0.0.2': 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-win.exe'
    },
    'macos': {
      '0.0.2': 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-mac.zip'
    },
    'linux': {
      '0.0.2': 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-linux.AppImage'
    }
  };
  
  const normalizedPlatform = platform.toLowerCase();
  const platformUrls = versionUrls[normalizedPlatform];
  
  if (platformUrls && platformUrls[version]) {
    const directUrl = platformUrls[version];
    logger.info(`DIRECT REDIRECT: Redirecting ${platform} v${version} to ${directUrl}`);
    
    // Log download attempt (optional)
    try {
      await downloadService.logDownload({
        platform: normalizedPlatform,
        version,
        architecture: 'x64',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date()
      });
    } catch (error) {
      // Don't fail if logging fails
      logger.warn(`Failed to log download: ${error.message}`);
    }
    
    return res.redirect(directUrl);
  }
  
  logger.error(`DIRECT REDIRECT: No URL found for ${platform} v${version}`);
  res.status(404).json({
    success: false,
    message: 'Descarga no disponible para esta plataforma/versión'
  });
}));

// Endpoint para verificar integridad de archivos
router.get('/checksum/:platform/:version', asyncHandler(async (req, res) => {
  const { platform, version } = req.params;
  
  try {
    const checksums = await downloadService.getFileChecksums(platform, version);
    
    if (!checksums) {
      return res.status(404).json({
        success: false,
        message: 'Checksums no disponibles'
      });
    }
    
    res.json({
      success: true,
      checksums
    });
  } catch (error) {
    logger.error(`Error al obtener checksums ${platform} v${version}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener checksums'
    });
  }
}));

// Estadísticas de descarga (solo para admins)
router.get('/stats', asyncHandler(async (req, res) => {
  // TODO: Implementar autenticación de admin
  
  try {
    const stats = await downloadService.getDownloadStats();
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    logger.error('Error al obtener estadísticas de descarga:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener estadísticas'
    });
  }
}));

module.exports = router;
