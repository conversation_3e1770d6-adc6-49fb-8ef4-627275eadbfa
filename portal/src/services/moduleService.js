const path = require('path');
const fs = require('fs').promises;
const config = require('../config');
const logger = require('../middleware/logger');

class ModuleService {
  constructor() {
    this.modulesPath = path.join(__dirname, '../../downloads/modules');
    this.downloadStats = new Map();
    
    // Module definitions from config
    this.availableModules = config.modules || {};
  }

  // Get all available modules with their information
  async getAllModules() {
    try {
      const modules = [];
      
      for (const [moduleId, moduleConfig] of Object.entries(this.availableModules)) {
        const moduleInfo = await this.getModuleInfo(moduleId);
        modules.push({
          id: moduleId,
          ...moduleConfig,
          ...moduleInfo,
          downloadUrl: `/modules/${moduleId}/download`,
          infoUrl: `/modules/${moduleId}`
        });
      }
      
      return modules;
    } catch (error) {
      logger.error('Error al obtener módulos:', error);
      return [];
    }
  }

  // Get specific module information
  async getModuleInfo(moduleId) {
    try {
      if (!this.availableModules[moduleId]) {
        return null;
      }

      // Check if module has downloadable versions
      const modulePath = path.join(this.modulesPath, moduleId);
      const versions = await this.getModuleVersions(moduleId);
      const latestVersion = versions.length > 0 ? versions[0] : null;
      
      let manifest = null;
      let downloadable = false;
      let packageSize = 0;

      if (latestVersion) {
        const manifestPath = path.join(modulePath, latestVersion, 'manifest.json');
        try {
          const manifestContent = await fs.readFile(manifestPath, 'utf8');
          manifest = JSON.parse(manifestContent);
          downloadable = true;
          
          // Get package size
          const packagePath = path.join(modulePath, latestVersion, `${moduleId}-${latestVersion}.tar.gz`);
          try {
            const stats = await fs.stat(packagePath);
            packageSize = stats.size;
          } catch (e) {
            // Package file doesn't exist
          }
        } catch (e) {
          logger.warn(`No manifest found for ${moduleId} v${latestVersion}`);
        }
      }

      return {
        versions,
        latestVersion,
        downloadable,
        packageSize: this.formatBytes(packageSize),
        downloads: this.getDownloadCount(moduleId),
        manifest: manifest || {},
        status: downloadable ? 'available' : 'coming_soon'
      };
    } catch (error) {
      logger.error(`Error al obtener info del módulo ${moduleId}:`, error);
      return null;
    }
  }

  // Get available versions for a module
  async getModuleVersions(moduleId) {
    try {
      const modulePath = path.join(this.modulesPath, moduleId);
      const entries = await fs.readdir(modulePath, { withFileTypes: true });
      
      const versionDirs = entries
        .filter(entry => entry.isDirectory() && /^\d+\.\d+\.\d+$/.test(entry.name))
        .map(entry => entry.name);
      
      // Filter to only include versions that have valid manifests and packages
      const validVersions = [];
      for (const version of versionDirs) {
        const manifestPath = path.join(modulePath, version, 'manifest.json');
        const packagePath = path.join(modulePath, version, `${moduleId}-${version}.tar.gz`);
        
        try {
          // Check if both manifest and package exist
          await fs.access(manifestPath);
          await fs.access(packagePath);
          validVersions.push(version);
        } catch (e) {
          // Skip versions without manifest or package
          continue;
        }
      }
      
      // Sort valid versions in descending order (latest first)
      validVersions.sort((a, b) => {
        const versionA = a.substring(1).split('.').map(n => parseInt(n));
        const versionB = b.substring(1).split('.').map(n => parseInt(n));
        
        for (let i = 0; i < Math.max(versionA.length, versionB.length); i++) {
          const numA = versionA[i] || 0;
          const numB = versionB[i] || 0;
          if (numA !== numB) {
            return numB - numA;
          }
        }
        return 0;
      });

      return validVersions;
    } catch (error) {
      logger.error(`Error al obtener versiones de ${moduleId}:`, error);
      return [];
    }
  }

  // Get download URL for a specific module version
  async getDownloadUrl(moduleId, version = null) {
    try {
      const moduleInfo = await this.getModuleInfo(moduleId);
      if (!moduleInfo || !moduleInfo.downloadable) {
        return null;
      }

      const targetVersion = version || moduleInfo.latestVersion;
      if (!targetVersion) {
        return null;
      }

      const packagePath = path.join(this.modulesPath, moduleId, targetVersion, `${moduleId}-${targetVersion}.tar.gz`);
      
      // Verify package exists
      try {
        await fs.access(packagePath);
        return {
          moduleId,
          version: targetVersion,
          filename: `${moduleId}-${targetVersion}.tar.gz`,
          path: packagePath,
          type: 'local'
        };
      } catch {
        return null;
      }
    } catch (error) {
      logger.error(`Error al obtener URL de descarga ${moduleId}:`, error);
      return null;
    }
  }

  // Log module download
  async logDownload(downloadInfo) {
    try {
      const logEntry = {
        ...downloadInfo,
        id: Date.now().toString(),
        timestamp: new Date(),
        ip: downloadInfo.ip || 'unknown',
        userAgent: downloadInfo.userAgent || 'unknown'
      };
      
      // Log to file
      logger.info('Descarga de módulo registrada:', logEntry);
      
      // Update stats in memory
      const key = `${downloadInfo.moduleId}_${downloadInfo.version}`;
      if (!this.downloadStats.has(key)) {
        this.downloadStats.set(key, 0);
      }
      this.downloadStats.set(key, this.downloadStats.get(key) + 1);
      
      return logEntry;
    } catch (error) {
      logger.error('Error al registrar descarga de módulo:', error);
      throw error;
    }
  }

  // Get download statistics
  getDownloadStats() {
    try {
      const stats = {
        totalDownloads: Array.from(this.downloadStats.values()).reduce((a, b) => a + b, 0),
        downloadsByModule: {},
        topModules: []
      };

      // Calculate downloads by module
      for (const [key, count] of this.downloadStats.entries()) {
        const [moduleId] = key.split('_');
        if (!stats.downloadsByModule[moduleId]) {
          stats.downloadsByModule[moduleId] = 0;
        }
        stats.downloadsByModule[moduleId] += count;
      }

      // Get top modules
      stats.topModules = Object.entries(stats.downloadsByModule)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([moduleId, downloads]) => ({
          moduleId,
          moduleName: this.availableModules[moduleId]?.name || moduleId,
          downloads
        }));

      return stats;
    } catch (error) {
      logger.error('Error al obtener estadísticas de descargas:', error);
      return {
        totalDownloads: 0,
        downloadsByModule: {},
        topModules: []
      };
    }
  }

  // Get download count for a specific module
  getDownloadCount(moduleId) {
    let totalDownloads = 0;
    for (const [key, count] of this.downloadStats.entries()) {
      if (key.startsWith(`${moduleId}_`)) {
        totalDownloads += count;
      }
    }
    return totalDownloads;
  }

  // Verify module compatibility
  async verifyCompatibility(moduleId, version = null) {
    try {
      const moduleInfo = await this.getModuleInfo(moduleId);
      if (!moduleInfo || !moduleInfo.manifest) {
        return { compatible: false, reason: 'Module not found or no manifest' };
      }

      const manifest = moduleInfo.manifest;
      const compatibility = manifest.compatibility || {};

      // Check CoreDesk version compatibility
      if (compatibility.coredesk) {
        const requiredVersion = compatibility.coredesk.replace('>=', '');
        const currentVersion = config.appVersion;
        
        if (!this.compareVersions(currentVersion, requiredVersion)) {
          return {
            compatible: false,
            reason: `Requires CoreDesk ${compatibility.coredesk}, current version: ${currentVersion}`
          };
        }
      }

      return { compatible: true };
    } catch (error) {
      logger.error(`Error al verificar compatibilidad ${moduleId}:`, error);
      return { compatible: false, reason: 'Error checking compatibility' };
    }
  }

  // Compare version strings (simple implementation)
  compareVersions(current, required) {
    const currentParts = current.split('.').map(n => parseInt(n));
    const requiredParts = required.split('.').map(n => parseInt(n));
    
    for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
      const currentNum = currentParts[i] || 0;
      const requiredNum = requiredParts[i] || 0;
      
      if (currentNum > requiredNum) return true;
      if (currentNum < requiredNum) return false;
    }
    return true; // Equal versions
  }

  // Format bytes to human readable format
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Get module categories
  getModuleCategories() {
    const categories = new Set();
    
    for (const moduleConfig of Object.values(this.availableModules)) {
      if (moduleConfig.category) {
        categories.add(moduleConfig.category);
      }
    }
    
    return Array.from(categories);
  }

  // Search modules
  async searchModules(query, category = null) {
    try {
      const allModules = await this.getAllModules();
      
      return allModules.filter(module => {
        const matchesQuery = !query || 
          module.name.toLowerCase().includes(query.toLowerCase()) ||
          module.description.toLowerCase().includes(query.toLowerCase()) ||
          (module.features && module.features.some(feature => 
            feature.toLowerCase().includes(query.toLowerCase())
          ));
          
        const matchesCategory = !category || module.category === category;
        
        return matchesQuery && matchesCategory;
      });
    } catch (error) {
      logger.error('Error al buscar módulos:', error);
      return [];
    }
  }
}

module.exports = new ModuleService();