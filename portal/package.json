{"name": "coredesk-portal", "version": "0.0.2", "description": "Portal web oficial de CoreDesk - Plataforma legal y contable profesional", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build": "npm run build:css", "build:css": "sass src/public/scss/main.scss src/public/css/main.css --style compressed", "test": "jest", "lint": "eslint src/"}, "keywords": ["coredesk", "legal", "contable", "lexflow", "protocolx", "auditpro", "finsync", "mcp", "asistentes-virtuales"], "author": "CoreDesk Team", "license": "ISC", "dependencies": {"axios": "^1.4.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "ejs": "^3.1.9", "express": "^4.18.2", "express-ejs-layouts": "^2.5.1", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "js-yaml": "^4.1.0", "node-cron": "^3.0.2", "nodemailer": "^7.0.3", "semver": "^7.5.0", "winston": "^3.8.2"}, "devDependencies": {"eslint": "^8.40.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "sass": "^1.62.1"}, "engines": {"node": ">=16.0.0"}}