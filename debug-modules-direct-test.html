<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoreDesk - Debug Module Test</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://api.coredeskpro.com https://*.coredeskpro.com https://portal.coredeskpro.com https://coredeskpro.com http://localhost:* https://localhost:*;">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .debug-section {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        .module-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(139, 125, 216, 0.3);
            transform: translateY(-2px);
        }
        .install-btn, .install-third-party-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .install-btn:hover, .install-third-party-btn:hover {
            background: linear-gradient(135deg, #45a049, #3d8b40);
            transform: translateY(-2px);
        }
        .debug-log {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #404040;
        }
        .status-success { color: #4CAF50; }
        .status-error { color: #f44336; }
        .status-warning { color: #ff9800; }
        .status-info { color: #2196f3; }
        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top-color: #8b7dd8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .module-dropzone-section {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .module-dropzone {
            border: 2px dashed rgba(139, 125, 216, 0.3);
            border-radius: 12px;
            padding: 30px 20px;
            text-align: center;
            background: rgba(139, 125, 216, 0.05);
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🧪 CoreDesk Module Loading Debug Test</h1>
        <p><strong>This test directly loads modules using the EXACT same code as CoreDesk</strong></p>

        <div class="debug-section">
            <h2>🔧 Environment Information</h2>
            <div id="env-info"></div>
        </div>

        <div class="debug-section">
            <h2>📡 URL Configuration Test</h2>
            <div id="url-config"></div>
        </div>

        <div class="debug-section">
            <h2>📦 Module Loading Test</h2>
            <button onclick="loadModules()" style="margin-bottom: 20px;">🚀 Load Modules (Simulate CoreDesk)</button>
            <div id="module-grid"></div>
        </div>

        <div class="debug-section">
            <h2>📋 Debug Log</h2>
            <div id="debug-log" class="debug-log">Waiting for test execution...\n</div>
        </div>
    </div>

    <script>
        // Copy the exact URL logic from CoreDesk app.js
        function getModuleRepositoryUrl() {
            // Check if there's a configured URL in CoreDeskAuth
            if (window.CoreDeskAuth?.api?.moduleRepositoryUrl) {
                return window.CoreDeskAuth.api.moduleRepositoryUrl;
            }
            
            // FORCED PRODUCTION URL - Always use production server for module loading
            // This ensures consistent behavior regardless of environment
            console.log('[CoreDeskApp] FORCE PRODUCTION: Using production module repository');
            return 'https://coredeskpro.com/api/modules';
        }

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            const typeClass = `status-${type}`;
            logElement.innerHTML += `<span class="${typeClass}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        function isModuleInstalled(moduleId) {
            // Simulate installed check
            return false; // For testing, assume nothing is installed
        }

        function toggleModuleDropzoneVisibility() {
            const dropzone = document.querySelector('.module-dropzone-section');
            if (dropzone) {
                const isHidden = dropzone.style.display === 'none';
                dropzone.style.display = isHidden ? 'block' : 'none';
                log(`Dropzone ${isHidden ? 'shown' : 'hidden'}`, 'info');
            }
        }

        async function loadModules() {
            const moduleGrid = document.getElementById('module-grid');
            
            log('Starting module loading test...', 'info');
            
            try {
                // Show loading state
                moduleGrid.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div class="loading-spinner"></div>
                        <p>🔄 Connecting to server...</p>
                        <small>Loading modules from production API</small>
                    </div>
                `;

                // Get URL and log it
                const moduleUrl = getModuleRepositoryUrl();
                log(`Module URL: ${moduleUrl}`, 'info');
                log(`User Agent: ${navigator.userAgent}`, 'info');
                log(`Location: ${window.location.hostname}`, 'info');
                log('*** FORCING PRODUCTION URLS ***', 'warning');

                // Force cache bypass
                const cacheBuster = Date.now();
                const finalUrl = `${moduleUrl}?cb=${cacheBuster}`;
                log(`Cache-busted URL: ${finalUrl}`, 'info');

                const response = await fetch(finalUrl, {
                    cache: 'no-cache',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                log(`API Response received`, 'success');
                log(`Modules found: ${data.modules?.length || 0}`, 'success');

                if (!data.success || !data.modules) {
                    throw new Error('Invalid API response');
                }

                const modules = data.modules;
                log('*** SUCCESS - MODULES LOADED FROM PRODUCTION ***', 'success');

                // Generate module cards (copied from CoreDesk)
                const moduleCards = modules.map(module => {
                    const isInstalled = isModuleInstalled(module.id);
                    const isDownloadable = module.downloadUrl && module.downloadUrl.trim() !== '';
                    
                    let statusText, statusClass, actionButton;
                    if (isInstalled) {
                        statusText = 'Instalado';
                        statusClass = 'status-installed';
                        actionButton = `<button class="uninstall-btn" data-module="${module.id}">🗑️ Desinstalar</button>`;
                    } else if (isDownloadable) {
                        statusText = 'Disponible';
                        statusClass = 'status-available';
                        actionButton = `<button class="install-btn" data-module="${module.id}" data-url="${module.downloadUrl}">📥 Instalar</button>`;
                    } else {
                        statusText = 'No disponible';
                        statusClass = 'status-unavailable';
                        actionButton = '';
                    }
                    
                    return `
                        <div class="module-card">
                            <h4>${module.name}</h4>
                            <p>${module.description}</p>
                            <div>
                                <small>Versión: ${module.version || 'N/A'}</small><br>
                                <small>Estado: <span class="${statusClass}">${statusText}</span></small>
                            </div>
                            <div style="margin-top: 15px;">
                                ${actionButton}
                            </div>
                        </div>
                    `;
                }).join('');

                // Display the modules with the exact same header as CoreDesk
                moduleGrid.innerHTML = `
                    <div style="margin-bottom: 20px;">
                        <h3 style="color: #4CAF50;">🚀 Módulos del Servidor (PRODUCTION)</h3>
                        <small style="color: #4CAF50; font-weight: bold;">✅ Conectado a: https://coredeskpro.com/api/modules</small>
                        <br><br>
                        <button id="install-third-party-btn" class="install-third-party-btn" onclick="toggleModuleDropzoneVisibility()">
                            📥 Instalar Módulo de Terceros
                        </button>
                    </div>
                    <div>
                        ${moduleCards}
                    </div>
                    
                    <!-- Third-Party Module Installation Zone (Hidden by default) -->
                    <div class="module-dropzone-section" style="display: none;">
                        <div>
                            <h4>Instalación de Módulos de Terceros</h4>
                            <small>Arrastra archivos .tar.gz o .zip aquí para instalar</small>
                        </div>
                        <div class="module-dropzone">
                            <div>
                                <p style="font-size: 48px; margin: 0;">📥</p>
                                <p>Arrastra módulos aquí</p>
                                <small>Formatos compatibles: .tar.gz, .zip</small>
                                <br><br>
                                <button class="install-third-party-btn">📁 Explorar archivos</button>
                            </div>
                        </div>
                    </div>
                `;

                log('*** MODULE GRID UPDATED WITH PRODUCTION DATA ***', 'success');
                log('*** THIRD-PARTY BUTTON SHOULD BE VISIBLE ***', 'success');

                // Add click handlers for install buttons
                const installButtons = moduleGrid.querySelectorAll('.install-btn');
                installButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        const moduleId = button.getAttribute('data-module');
                        log(`Install clicked for module: ${moduleId}`, 'info');
                        alert(`Install module: ${moduleId}`);
                    });
                });

            } catch (error) {
                log(`ERROR: ${error.message}`, 'error');
                moduleGrid.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #f44336;">
                        <h3>❌ Connection Failed</h3>
                        <p>Could not load modules from server</p>
                        <small>Error: ${error.message}</small>
                        <br><br>
                        <button onclick="loadModules()">🔄 Retry</button>
                    </div>
                `;
            }
        }

        // Initialize environment info
        document.addEventListener('DOMContentLoaded', function() {
            const envInfo = document.getElementById('env-info');
            const isElectron = navigator.userAgent.toLowerCase().indexOf(' electron/') > -1;
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            
            envInfo.innerHTML = `
                <p><strong>Hostname:</strong> ${window.location.hostname}</p>
                <p><strong>Is Electron:</strong> ${isElectron ? 'Yes' : 'No'}</p>
                <p><strong>Is Localhost:</strong> ${isLocalhost ? 'Yes' : 'No'}</p>
                <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
            `;

            const urlConfig = document.getElementById('url-config');
            const configuredUrl = getModuleRepositoryUrl();
            urlConfig.innerHTML = `
                <p><strong>Configured URL:</strong> <span style="color: #4CAF50;">${configuredUrl}</span></p>
                <p><strong>Expected URL:</strong> https://coredeskpro.com/api/modules</p>
                <p><strong>Status:</strong> ${configuredUrl === 'https://coredeskpro.com/api/modules' ? '✅ CORRECT' : '❌ INCORRECT'}</p>
            `;

            log('Debug environment initialized', 'info');
            log('Run the module loading test to verify connectivity', 'info');
        });
    </script>
</body>
</html>