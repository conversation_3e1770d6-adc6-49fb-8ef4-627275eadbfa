#!/bin/bash

# CoreDesk Module Deployment Script
# Deploys packaged modules to production server

set -e

# Production server configuration (using SSH config)
PROD_HOST="coredesk-prod"
PROD_PATH="/opt/coredesk/data/downloads/modules"
LOCAL_MODULES_PATH="/home/<USER>/coredesk/portal/downloads/modules"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 CoreDesk Module Deployment Script${NC}"
echo -e "${BLUE}====================================${NC}"

# Check if local modules exist
if [ ! -d "$LOCAL_MODULES_PATH" ]; then
    echo -e "${RED}❌ Local modules directory not found: $LOCAL_MODULES_PATH${NC}"
    exit 1
fi

# Check if modules have been packaged
MODULE_COUNT=$(find "$LOCAL_MODULES_PATH" -name "*.tar.gz" | wc -l)
if [ "$MODULE_COUNT" -eq 0 ]; then
    echo -e "${RED}❌ No packaged modules found. Please run 'npm run package-modules' first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found $MODULE_COUNT packaged modules${NC}"

# Test SSH connection
echo -e "${YELLOW}🔍 Testing SSH connection to production server...${NC}"
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$PROD_HOST" exit 2>/dev/null; then
    echo -e "${RED}❌ Cannot connect to production server. Please check:${NC}"
    echo -e "${RED}   - SSH key is configured${NC}"
    echo -e "${RED}   - Server is accessible${NC}"
    echo -e "${RED}   - Credentials are correct${NC}"
    exit 1
fi

echo -e "${GREEN}✅ SSH connection successful${NC}"

# Create remote directory structure
echo -e "${YELLOW}📁 Creating remote directory structure...${NC}"
ssh "$PROD_HOST" "mkdir -p $PROD_PATH"

# Get list of modules to deploy
MODULES=($(find "$LOCAL_MODULES_PATH" -mindepth 1 -maxdepth 1 -type d -exec basename {} \;))

echo -e "${BLUE}📦 Deploying ${#MODULES[@]} modules...${NC}"

# Deploy each module
for MODULE in "${MODULES[@]}"; do
    echo -e "${YELLOW}🔄 Deploying $MODULE...${NC}"
    
    # Create remote module directory
    ssh "$PROD_HOST" "mkdir -p $PROD_PATH/$MODULE"
    
    # Copy module files
    rsync -avz --progress "$LOCAL_MODULES_PATH/$MODULE/" "$PROD_HOST:$PROD_PATH/$MODULE/"
    
    # Verify deployment
    REMOTE_FILES=$(ssh "$PROD_HOST" "find $PROD_PATH/$MODULE -name '*.tar.gz' | wc -l")
    if [ "$REMOTE_FILES" -gt 0 ]; then
        echo -e "${GREEN}✅ $MODULE deployed successfully ($REMOTE_FILES packages)${NC}"
    else
        echo -e "${RED}❌ $MODULE deployment failed${NC}"
    fi
done

# Verify overall deployment
echo -e "${BLUE}🔍 Verifying deployment...${NC}"
TOTAL_REMOTE_FILES=$(ssh "$PROD_HOST" "find $PROD_PATH -name '*.tar.gz' | wc -l")
TOTAL_LOCAL_FILES=$(find "$LOCAL_MODULES_PATH" -name "*.tar.gz" | wc -l)

if [ "$TOTAL_REMOTE_FILES" -eq "$TOTAL_LOCAL_FILES" ]; then
    echo -e "${GREEN}✅ All modules deployed successfully!${NC}"
    echo -e "${GREEN}   📦 Total packages: $TOTAL_REMOTE_FILES${NC}"
    echo -e "${GREEN}   🌍 Production URL: https://coredeskpro.com/api/modules${NC}"
else
    echo -e "${RED}❌ Deployment verification failed${NC}"
    echo -e "${RED}   Local files: $TOTAL_LOCAL_FILES${NC}"
    echo -e "${RED}   Remote files: $TOTAL_REMOTE_FILES${NC}"
fi

# Set proper permissions
echo -e "${YELLOW}🔧 Setting proper permissions...${NC}"
ssh "$PROD_HOST" "chown -R www-data:www-data $PROD_PATH && chmod -R 755 $PROD_PATH"

echo -e "${BLUE}🎉 Deployment completed!${NC}"
echo -e "${BLUE}You can now test the modules at: https://coredeskpro.com/api/modules${NC}"