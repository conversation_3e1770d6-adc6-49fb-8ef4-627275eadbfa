#!/bin/bash

# CoreDesk Module Build and Deploy Script
# Packages modules and deploys them to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 CoreDesk Module Build & Deploy Pipeline${NC}"
echo -e "${BLUE}==========================================${NC}"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Step 1: Package modules
echo -e "${YELLOW}📦 Step 1: Packaging modules...${NC}"
cd "$PROJECT_ROOT"
node scripts/package-modules.js

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Modules packaged successfully${NC}"
else
    echo -e "${RED}❌ Module packaging failed${NC}"
    exit 1
fi

# Step 2: Deploy to production (optional)
echo -e "${YELLOW}🚀 Step 2: Deploy to production? (y/n)${NC}"
read -p "Continue with deployment? " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🌍 Deploying to production...${NC}"
    "$SCRIPT_DIR/deploy-modules.sh"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Production deployment completed${NC}"
    else
        echo -e "${RED}❌ Production deployment failed${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}ℹ️  Deployment skipped. Modules are ready for manual deployment.${NC}"
fi

echo -e "${BLUE}🎉 Build and deploy pipeline completed!${NC}"
echo -e "${BLUE}Local modules are available at: portal/downloads/modules${NC}"