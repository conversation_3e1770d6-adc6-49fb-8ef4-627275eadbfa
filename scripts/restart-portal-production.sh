#!/bin/bash

# Production Portal Container Restart Script
# Restarts portal container to detect newly uploaded modules

set -e

# Production server configuration (using SSH config)
PROD_HOST="coredesk-prod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 CoreDesk Production Portal Restart${NC}"
echo -e "${BLUE}====================================${NC}"

# Test SSH connection
echo -e "${YELLOW}🔍 Testing SSH connection...${NC}"
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$PROD_HOST" exit 2>/dev/null; then
    echo -e "${RED}❌ Cannot connect to production server${NC}"
    exit 1
fi

echo -e "${GREEN}✅ SSH connection successful${NC}"

# Check if modules are uploaded
echo -e "${YELLOW}📁 Checking uploaded modules...${NC}"
MODULE_COUNT=$(ssh "$PROD_HOST" "find /opt/coredesk/data/downloads/modules -name '*.tar.gz' 2>/dev/null | wc -l" || echo "0")

if [ "$MODULE_COUNT" -eq 0 ]; then
    echo -e "${RED}❌ No module packages found on server${NC}"
    echo -e "${RED}   Please upload modules first using: npm run deploy-modules${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found $MODULE_COUNT module packages${NC}"

# Fix file permissions
echo -e "${YELLOW}🔧 Fixing file permissions...${NC}"
ssh "$PROD_HOST" "
    chown -R www-data:www-data /opt/coredesk/data/downloads/modules/ 2>/dev/null || \
    chown -R 1000:1000 /opt/coredesk/data/downloads/modules/ 2>/dev/null || \
    chmod -R 755 /opt/coredesk/data/downloads/modules/
"

# Restart portal container
echo -e "${YELLOW}🔄 Restarting portal container...${NC}"
ssh "$PROD_HOST" "
    cd /opt/coredesk && 
    docker-compose restart portal &&
    echo 'Portal container restarted successfully'
"

# Wait for container to be ready
echo -e "${YELLOW}⏳ Waiting for portal to be ready...${NC}"
sleep 10

# Test portal API
echo -e "${YELLOW}🧪 Testing portal API...${NC}"
PORTAL_STATUS=$(ssh "$PROD_HOST" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000/api/modules 2>/dev/null || echo '000'")

if [ "$PORTAL_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ Portal API is responding${NC}"
    
    # Check if modules are now available
    echo -e "${YELLOW}🔍 Checking module availability...${NC}"
    AVAILABLE_MODULES=$(ssh "$PROD_HOST" "curl -s http://localhost:3000/api/modules 2>/dev/null | grep -o '\"downloadable\":true' | wc -l || echo '0'")
    
    if [ "$AVAILABLE_MODULES" -gt 0 ]; then
        echo -e "${GREEN}✅ $AVAILABLE_MODULES modules are now available!${NC}"
        echo -e "${GREEN}🎉 Portal restart successful - modules should now appear in CoreDesk${NC}"
    else
        echo -e "${YELLOW}⚠️  Portal is running but modules still show as 'coming_soon'${NC}"
        echo -e "${YELLOW}   This might indicate a deeper configuration issue${NC}"
    fi
else
    echo -e "${RED}❌ Portal API not responding (HTTP $PORTAL_STATUS)${NC}"
    echo -e "${RED}   Check container logs: docker logs srv-portal${NC}"
fi

echo -e "${BLUE}🌍 Test the modules at: https://coredeskpro.com/api/modules${NC}"