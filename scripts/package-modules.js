#!/usr/bin/env node

/**
 * Module Packaging Script for CoreDesk
 * Creates .tar.gz packages from module source files
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

const MODULES_SOURCE = path.join(__dirname, '../coredesk/src/js');
const MODULES_PACKAGES = path.join(__dirname, '../coredesk/src/js/packages');
const MODULES_CSS = path.join(__dirname, '../coredesk/src/css');
const PORTAL_DOWNLOADS = path.join(__dirname, '../portal/downloads/modules');

// Module configurations
const MODULES = {
  lexflow: {
    version: '0.0.2',
    sourceDir: 'modules/lexflow',
    packageDir: 'packages/lexflow',
    cssFile: 'auth/login.css', // Using existing CSS file for now
    hasImplementation: true
  },
  protocolx: {
    version: '0.0.2',
    sourceDir: 'modules/protocolx',
    packageDir: 'packages/protocolx',
    cssFile: 'auth/login.css', // Using existing CSS file for now
    hasImplementation: true
  },
  auditpro: {
    version: '0.0.1',
    sourceDir: null,
    packageDir: 'packages/auditpro',
    cssFile: 'auth/login.css', // Using existing CSS file for now
    hasImplementation: false
  },
  finsync: {
    version: '0.0.1',
    sourceDir: null,
    packageDir: 'packages/finsync',
    cssFile: 'auth/login.css', // Using existing CSS file for now
    hasImplementation: false
  }
};

async function calculateChecksum(filePath) {
  try {
    const fileBuffer = await fs.readFile(filePath);
    const hashSum = crypto.createHash('sha256');
    hashSum.update(fileBuffer);
    return hashSum.digest('hex');
  } catch (error) {
    console.error(`Error calculating checksum for ${filePath}:`, error.message);
    return null;
  }
}

async function getFileSize(filePath) {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch (error) {
    console.error(`Error getting file size for ${filePath}:`, error.message);
    return 0;
  }
}

async function createTempModuleDirectory(moduleId, config) {
  const tempDir = path.join(__dirname, `../temp/${moduleId}`);
  await fs.mkdir(tempDir, { recursive: true });

  // Copy package.json if exists
  const packageJsonPath = path.join(MODULES_PACKAGES, config.packageDir, 'package.json');
  try {
    await fs.copyFile(packageJsonPath, path.join(tempDir, 'package.json'));
  } catch (error) {
    console.log(`No package.json found for ${moduleId}, creating basic one`);
    const basicPackage = {
      id: moduleId,
      name: moduleId.charAt(0).toUpperCase() + moduleId.slice(1),
      version: config.version,
      main: `${moduleId}.js`,
      style: `${moduleId}.css`
    };
    await fs.writeFile(path.join(tempDir, 'package.json'), JSON.stringify(basicPackage, null, 2));
  }

  // Copy module implementation if exists
  if (config.hasImplementation && config.sourceDir) {
    const moduleSourcePath = path.join(MODULES_SOURCE, config.sourceDir);
    try {
      const files = await fs.readdir(moduleSourcePath);
      for (const file of files) {
        if (file.endsWith('.js')) {
          await fs.copyFile(
            path.join(moduleSourcePath, file),
            path.join(tempDir, file)
          );
        }
      }
    } catch (error) {
      console.log(`No module implementation found for ${moduleId}`);
    }
  }

  // Copy package wrapper if exists
  if (config.packageDir) {
    const packageSourcePath = path.join(MODULES_PACKAGES, config.packageDir);
    try {
      const files = await fs.readdir(packageSourcePath);
      for (const file of files) {
        if (file.endsWith('.js') && file !== 'package.json') {
          await fs.copyFile(
            path.join(packageSourcePath, file),
            path.join(tempDir, file)
          );
        }
      }
    } catch (error) {
      console.log(`No package wrapper found for ${moduleId}`);
    }
  }

  // Copy CSS file
  const cssSourcePath = path.join(MODULES_CSS, config.cssFile);
  try {
    await fs.copyFile(cssSourcePath, path.join(tempDir, `${moduleId}.css`));
  } catch (error) {
    console.log(`No CSS file found for ${moduleId}, creating empty one`);
    await fs.writeFile(path.join(tempDir, `${moduleId}.css`), '/* Module styles */\n');
  }

  // Create basic install.js if not exists
  const installPath = path.join(tempDir, 'install.js');
  try {
    await fs.access(installPath);
  } catch (error) {
    const basicInstall = `// Installation script for ${moduleId}\nconsole.log('Installing ${moduleId} module...');\n`;
    await fs.writeFile(installPath, basicInstall);
  }

  return tempDir;
}

async function packageModule(moduleId, config) {
  console.log(`\n📦 Packaging ${moduleId} v${config.version}...`);
  
  // Create temp directory with module files
  const tempDir = await createTempModuleDirectory(moduleId, config);
  
  // Create output directory
  const outputDir = path.join(PORTAL_DOWNLOADS, moduleId, config.version);
  await fs.mkdir(outputDir, { recursive: true });
  
  // Create tar.gz package
  const packageName = `${moduleId}-${config.version}.tar.gz`;
  const packagePath = path.join(outputDir, packageName);
  
  try {
    // Create tar.gz archive
    execSync(`cd "${tempDir}" && tar -czf "${packagePath}" .`, { stdio: 'inherit' });
    
    // Calculate checksum and size
    const checksum = await calculateChecksum(packagePath);
    const size = await getFileSize(packagePath);
    
    // Update manifest with package info
    const manifestPath = path.join(outputDir, 'manifest.json');
    const manifest = JSON.parse(await fs.readFile(manifestPath, 'utf8'));
    manifest.checksum = `sha256:${checksum}`;
    manifest.size = size;
    manifest.lastUpdated = new Date().toISOString();
    
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
    
    console.log(`✅ ${moduleId} packaged successfully`);
    console.log(`   📁 Package: ${packageName}`);
    console.log(`   📏 Size: ${Math.round(size / 1024)} KB`);
    console.log(`   🔐 Checksum: ${checksum.substring(0, 12)}...`);
    
  } catch (error) {
    console.error(`❌ Error packaging ${moduleId}:`, error.message);
  }
  
  // Clean up temp directory
  await fs.rm(tempDir, { recursive: true, force: true });
}

async function main() {
  console.log('🚀 CoreDesk Module Packaging Script');
  console.log('=====================================');
  
  // Ensure temp directory exists
  await fs.mkdir(path.join(__dirname, '../temp'), { recursive: true });
  
  // Package all modules
  for (const [moduleId, config] of Object.entries(MODULES)) {
    await packageModule(moduleId, config);
  }
  
  // Clean up temp directory
  await fs.rm(path.join(__dirname, '../temp'), { recursive: true, force: true });
  
  console.log('\n✨ All modules packaged successfully!');
  console.log(`📂 Packages available at: ${PORTAL_DOWNLOADS}`);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { packageModule, MODULES };