#!/usr/bin/env node

/**
 * CoreDesk Module Connectivity Test
 * Tests the module API connectivity and URL configuration
 */

const https = require('https');
const http = require('http');

console.log('🧪 CoreDesk Module Connectivity Test');
console.log('=====================================\n');

// Test URLs
const testUrls = [
    {
        name: 'Production API',
        url: 'https://coredeskpro.com/api/modules',
        protocol: https
    },
    {
        name: 'Production Website',
        url: 'https://coredeskpro.com',
        protocol: https
    },
    {
        name: 'Local Development API',
        url: 'http://localhost:3000/api/modules',
        protocol: http
    }
];

function testUrl(testConfig) {
    return new Promise((resolve) => {
        const { name, url, protocol } = testConfig;
        const urlObj = new URL(url);
        
        console.log(`🔍 Testing ${name}: ${url}`);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || (protocol === https ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            timeout: 5000,
            headers: {
                'User-Agent': 'CoreDesk/2.0.0 Test Script',
                'Accept': 'application/json'
            }
        };

        const req = protocol.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`   ✅ Status: ${res.statusCode} ${res.statusMessage}`);
                
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        if (jsonData.success && jsonData.modules) {
                            console.log(`   📦 Modules found: ${jsonData.modules.length}`);
                            jsonData.modules.forEach(module => {
                                console.log(`      • ${module.name} (${module.id}) - v${module.version}`);
                            });
                        } else {
                            console.log(`   ⚠️  Invalid JSON response format`);
                        }
                    } catch (e) {
                        console.log(`   ⚠️  Non-JSON response: ${data.substring(0, 100)}...`);
                    }
                } else {
                    console.log(`   ❌ Error response: ${data.substring(0, 200)}`);
                }
                
                resolve({ success: res.statusCode === 200, status: res.statusCode, url });
            });
        });

        req.on('error', (error) => {
            console.log(`   ❌ Connection error: ${error.message}`);
            resolve({ success: false, error: error.message, url });
        });

        req.on('timeout', () => {
            console.log(`   ⏰ Timeout (5s)`);
            req.destroy();
            resolve({ success: false, error: 'Timeout', url });
        });

        req.end();
    });
}

async function runTests() {
    const results = [];
    
    for (const testConfig of testUrls) {
        const result = await testUrl(testConfig);
        results.push(result);
        console.log(''); // Empty line between tests
    }
    
    // Summary
    console.log('📊 Test Summary');
    console.log('===============');
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ Successful: ${successful.length}/${results.length}`);
    if (successful.length > 0) {
        successful.forEach(result => {
            console.log(`   • ${result.url}`);
        });
    }
    
    if (failed.length > 0) {
        console.log(`❌ Failed: ${failed.length}/${results.length}`);
        failed.forEach(result => {
            console.log(`   • ${result.url} - ${result.error || 'HTTP ' + result.status}`);
        });
    }
    
    console.log('\n💡 Recommendations:');
    
    if (successful.some(r => r.url.includes('coredeskpro.com'))) {
        console.log('✅ Production server is accessible - URL fix should work');
        console.log('🔄 Please restart CoreDesk application to apply URL changes');
    } else {
        console.log('❌ Production server not accessible');
        console.log('🌐 Check internet connection and firewall settings');
    }
    
    if (failed.some(r => r.url.includes('localhost'))) {
        console.log('ℹ️  Local development server not running (this is normal for production use)');
    }
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Restart the CoreDesk application');
    console.log('2. Check the browser console for debug logs');
    console.log('3. Verify modules load from https://coredeskpro.com/api/modules');
}

// Environment detection simulation
console.log('🔧 Environment Detection Simulation');
console.log('====================================');

const mockEnvironments = [
    {
        name: 'Electron App',
        userAgent: 'Mozilla/5.0 Chrome/120.0.0.0 Electron/28.0.0 Safari/537.36',
        hostname: 'localhost',
        expected: 'https://coredeskpro.com/api/modules'
    },
    {
        name: 'Web Browser (localhost)',
        userAgent: 'Mozilla/5.0 Chrome/120.0.0.0 Safari/537.36',
        hostname: 'localhost',
        expected: 'https://coredeskpro.com/api/modules'
    },
    {
        name: 'Web Browser (domain)',
        userAgent: 'Mozilla/5.0 Chrome/120.0.0.0 Safari/537.36',
        hostname: 'app.example.com',
        expected: 'https://coredeskpro.com/api/modules'
    }
];

mockEnvironments.forEach(env => {
    const isElectron = env.userAgent.toLowerCase().includes('electron');
    const isLocalhost = env.hostname === 'localhost' || env.hostname === '127.0.0.1';
    
    let selectedUrl;
    if (false && isLocalhost && !isElectron) { // environment === 'development' (disabled)
        selectedUrl = 'http://localhost:3000/api/modules';
    } else {
        selectedUrl = 'https://coredeskpro.com/api/modules';
    }
    
    const isCorrect = selectedUrl === env.expected;
    console.log(`${isCorrect ? '✅' : '❌'} ${env.name}: ${selectedUrl}`);
    console.log(`   UserAgent: ${env.userAgent.substring(0, 50)}...`);
    console.log(`   Hostname: ${env.hostname}, Electron: ${isElectron}`);
    console.log('');
});

// Run the connectivity tests
runTests().catch(console.error);