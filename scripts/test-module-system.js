#!/usr/bin/env node

/**
 * Test CoreDesk Module System - Complete End-to-End Testing
 * Tests the fixed module loading and installation system
 */

const https = require('https');
const http = require('http');

// Test Configuration
const tests = [
    {
        name: 'Portal API - Development',
        url: 'http://localhost:3000/api/modules',
        expected: { modules: 4, downloadable: 4 }
    },
    {
        name: 'Portal API - Production', 
        url: 'https://coredeskpro.com/api/modules',
        expected: { modules: 4, downloadable: 4 }
    }
];

async function makeRequest(url) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        
        const req = protocol.get(url, { timeout: 10000 }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(data);
                    resolve({ status: res.statusCode, data: parsed, headers: res.headers });
                } catch (error) {
                    resolve({ status: res.statusCode, data: data, headers: res.headers });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
    });
}

async function testModuleAPI(test) {
    console.log(`\n🧪 Testing: ${test.name}`);
    console.log(`   URL: ${test.url}`);
    
    try {
        const response = await makeRequest(test.url);
        
        if (response.status !== 200) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        if (!response.data.success) {
            throw new Error('API response indicates failure');
        }
        
        const modules = response.data.modules;
        const downloadableModules = modules.filter(m => m.downloadable);
        
        console.log(`   ✅ Response: HTTP ${response.status}`);
        console.log(`   📦 Total modules: ${modules.length}`);
        console.log(`   💾 Downloadable: ${downloadableModules.length}`);
        
        // Detailed module info
        modules.forEach(module => {
            const status = module.downloadable ? '✅' : '⏳';
            console.log(`      ${status} ${module.name} v${module.version || 'N/A'} (${module.size || '0 B'})`);
        });
        
        // Verify expectations
        if (modules.length >= test.expected.modules && downloadableModules.length >= test.expected.downloadable) {
            console.log(`   🎉 PASS: All expectations met`);
            return true;
        } else {
            console.log(`   ❌ FAIL: Expected ${test.expected.modules} modules, ${test.expected.downloadable} downloadable`);
            return false;
        }
        
    } catch (error) {
        console.log(`   ❌ FAIL: ${error.message}`);
        return false;
    }
}

async function testModuleDownload(baseUrl, moduleId) {
    console.log(`\n📥 Testing download: ${moduleId}`);
    
    try {
        const downloadUrl = `${baseUrl}/${moduleId}/download`;
        const response = await makeRequest(downloadUrl);
        
        if (response.status === 200) {
            const contentType = response.headers['content-type'] || '';
            const contentLength = response.headers['content-length'] || '0';
            
            console.log(`   ✅ Download available`);
            console.log(`   📄 Content-Type: ${contentType}`);
            console.log(`   📏 Content-Length: ${contentLength} bytes`);
            return true;
        } else {
            console.log(`   ❌ Download failed: HTTP ${response.status}`);
            return false;
        }
        
    } catch (error) {
        console.log(`   ❌ Download error: ${error.message}`);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 CoreDesk Module System Test Suite');
    console.log('====================================');
    
    let totalTests = 0;
    let passedTests = 0;
    
    // Test API endpoints
    for (const test of tests) {
        totalTests++;
        const result = await testModuleAPI(test);
        if (result) passedTests++;
    }
    
    // Test downloads (only if production API is working)
    const prodUrl = 'https://coredeskpro.com/api/modules';
    try {
        const response = await makeRequest(prodUrl);
        if (response.status === 200 && response.data.success) {
            const downloadableModules = response.data.modules.filter(m => m.downloadable);
            
            for (const module of downloadableModules.slice(0, 2)) { // Test first 2 modules
                totalTests++;
                const result = await testModuleDownload(prodUrl, module.id);
                if (result) passedTests++;
            }
        }
    } catch (error) {
        console.log(`\n⚠️  Skipping download tests: ${error.message}`);
    }
    
    // Final results
    console.log(`\n📊 Test Results`);
    console.log(`================`);
    console.log(`   Total tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${totalTests - passedTests}`);
    console.log(`   Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log(`\n🎉 ALL TESTS PASSED!`);
        console.log(`✅ CoreDesk module system is fully functional`);
        console.log(`✅ Portal API serving 4 modules correctly`);
        console.log(`✅ Download endpoints working`);
        console.log(`✅ Ready for CoreDesk application testing`);
    } else {
        console.log(`\n⚠️  Some tests failed. Check the issues above.`);
    }
    
    console.log(`\n📋 Next Steps:`);
    console.log(`   1. Test CoreDesk application with updated code`);
    console.log(`   2. Verify modules display in "MÓDULOS DISPONIBLES"`);
    console.log(`   3. Test module installation functionality`);
    console.log(`   4. Test drag-and-drop for third-party modules`);
}

if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runAllTests, testModuleAPI, testModuleDownload };