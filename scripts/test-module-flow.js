#!/usr/bin/env node

/**
 * Test Module Flow - End-to-end testing of module system
 * Simulates the CoreDesk app fetching modules from portal
 */

const https = require('https');
const http = require('http');

const config = {
  // Test against local portal server
  baseUrl: 'http://localhost:3000/api/modules/',
  timeout: 10000
};

async function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, { timeout: config.timeout }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed, headers: res.headers });
        } catch (error) {
          resolve({ status: res.statusCode, data: data, headers: res.headers });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testModuleFlow() {
  console.log('🧪 CoreDesk Module Flow Test');
  console.log('============================');
  
  try {
    // Test 1: Fetch modules list
    console.log('\n📋 Test 1: Fetching modules list...');
    const modulesUrl = config.baseUrl.replace(/\/$/, '');
    const modulesResponse = await makeRequest(modulesUrl);
    
    if (modulesResponse.status !== 200) {
      throw new Error(`API returned status ${modulesResponse.status}`);
    }
    
    if (!modulesResponse.data.success) {
      throw new Error('API response indicates failure');
    }
    
    const modules = modulesResponse.data.modules;
    console.log(`✅ Found ${modules.length} modules`);
    
    // Display module information
    for (const module of modules) {
      const status = module.downloadable ? '✅ Available' : '⏳ Coming Soon';
      console.log(`   ${status} ${module.name} v${module.version || 'N/A'} (${module.size || '0 Bytes'})`);
    }
    
    // Test 2: Test download capabilities for available modules
    console.log('\n📦 Test 2: Testing module downloads...');
    
    const downloadableModules = modules.filter(m => m.downloadable);
    
    if (downloadableModules.length === 0) {
      console.log('⚠️  No downloadable modules found');
      return;
    }
    
    for (const module of downloadableModules.slice(0, 2)) { // Test first 2 modules
      console.log(`\n🔄 Testing download for ${module.name}...`);
      
      try {
        const downloadUrl = `${modulesUrl}/${module.id}/download`;
        const downloadResponse = await makeRequest(downloadUrl);
        
        if (downloadResponse.status === 200) {
          const contentType = downloadResponse.headers['content-type'] || '';
          const contentLength = downloadResponse.headers['content-length'] || '0';
          
          if (contentType.includes('application/gzip') || contentType.includes('application/octet-stream')) {
            console.log(`   ✅ Download available (${contentLength} bytes, ${contentType})`);
          } else {
            console.log(`   ⚠️  Unexpected content type: ${contentType}`);
          }
        } else {
          console.log(`   ❌ Download failed with status ${downloadResponse.status}`);
        }
      } catch (error) {
        console.log(`   ❌ Download error: ${error.message}`);
      }
    }
    
    // Test 3: Simulate CoreDesk module loading
    console.log('\n🔧 Test 3: Simulating CoreDesk module loading...');
    
    // This simulates what DynamicModuleManager would do
    const availableModules = modules.filter(m => m.downloadable && m.status === 'available');
    
    if (availableModules.length > 0) {
      console.log(`✅ CoreDesk would display ${availableModules.length} available modules:`);
      for (const module of availableModules) {
        console.log(`   📦 ${module.name} v${module.version} - ${module.description}`);
        console.log(`      Required License: ${module.manifest?.requiredLicense || 'N/A'}`);
        console.log(`      Compatibility: ${module.compatibility?.coredesk || 'N/A'}`);
      }
    } else {
      console.log('❌ No modules would be displayed in CoreDesk (this was the original problem)');
    }
    
    console.log('\n🎉 Module flow test completed successfully!');
    console.log('\n📝 Summary:');
    console.log(`   - Total modules configured: ${modules.length}`);
    console.log(`   - Available for download: ${downloadableModules.length}`);
    console.log(`   - Ready for CoreDesk: ${availableModules.length}`);
    
    if (availableModules.length > 0) {
      console.log('\n✅ The "No hay módulos disponibles" issue has been resolved!');
    } else {
      console.log('\n❌ The "No hay módulos disponibles" issue persists');
    }
    
  } catch (error) {
    console.log('\n❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting steps:');
    console.log('   1. Ensure portal server is running: cd portal && npm run dev');
    console.log('   2. Verify modules are packaged: npm run package-modules');
    console.log('   3. Check portal API: curl http://localhost:3000/api/modules');
    process.exit(1);
  }
}

if (require.main === module) {
  testModuleFlow();
}

module.exports = { testModuleFlow };