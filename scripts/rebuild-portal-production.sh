#!/bin/bash

# Production Portal Container Rebuild Script
# Rebuilds portal with updated moduleService.js and deploys

set -e

# Production server configuration (using SSH config)
PROD_HOST="coredesk-prod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 CoreDesk Portal Container Rebuild${NC}"
echo -e "${BLUE}====================================${NC}"

# Step 1: Build new portal image locally with latest code
echo -e "${YELLOW}📦 Building updated portal image...${NC}"
cd portal
docker build -t portal:0.0.3 .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Portal image build failed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Portal image built successfully${NC}"

# Step 2: Export image to tar file
echo -e "${YELLOW}📤 Exporting portal image...${NC}"
docker save portal:0.0.3 | gzip > ../docker-exports/portal-v0.0.3.tar.gz

echo -e "${GREEN}✅ Portal image exported to docker-exports/portal-v0.0.3.tar.gz${NC}"

# Step 3: Upload to production server
echo -e "${YELLOW}🚀 Uploading to production server...${NC}"
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$PROD_HOST" exit 2>/dev/null; then
    echo -e "${RED}❌ Cannot connect to production server${NC}"
    exit 1
fi

scp ../docker-exports/portal-v0.0.3.tar.gz "$PROD_HOST:/opt/coredesk/"

# Step 4: Load and deploy on production
echo -e "${YELLOW}🔄 Loading and deploying on production...${NC}"
ssh "$PROD_HOST" "
    cd /opt/coredesk &&
    echo 'Loading new portal image...' &&
    docker load < portal-v0.0.3.tar.gz &&
    echo 'Updating docker-compose to use new image...' &&
    sed -i 's/image: portal:0.0.2/image: portal:0.0.3/' docker-compose.yml &&
    echo 'Stopping current portal...' &&
    docker-compose stop portal &&
    echo 'Removing old portal container...' &&
    docker-compose rm -f portal &&
    echo 'Starting new portal...' &&
    docker-compose up -d portal &&
    echo 'Waiting for portal to be ready...' &&
    sleep 15
"

# Step 5: Verify deployment
echo -e "${YELLOW}🧪 Verifying deployment...${NC}"
PORTAL_STATUS=$(ssh "$PROD_HOST" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000/api/modules 2>/dev/null || echo '000'")

if [ "$PORTAL_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ Portal is responding${NC}"
    
    # Check if modules are now available
    AVAILABLE_MODULES=$(ssh "$PROD_HOST" "curl -s http://localhost:3000/api/modules 2>/dev/null | grep -o '\"downloadable\":true' | wc -l || echo '0'")
    
    if [ "$AVAILABLE_MODULES" -gt 0 ]; then
        echo -e "${GREEN}🎉 SUCCESS! $AVAILABLE_MODULES modules are now available!${NC}"
        echo -e "${GREEN}✅ Portal rebuild completed - modules should now appear in CoreDesk${NC}"
    else
        echo -e "${YELLOW}⚠️  Portal is running but modules still not available${NC}"
        echo -e "${YELLOW}   Checking detailed response...${NC}"
        ssh "$PROD_HOST" "curl -s http://localhost:3000/api/modules | head -200"
    fi
else
    echo -e "${RED}❌ Portal not responding (HTTP $PORTAL_STATUS)${NC}"
    echo -e "${RED}   Checking container status...${NC}"
    ssh "$PROD_HOST" "docker ps | grep portal"
fi

# Step 6: Clean up local export
rm -f ../docker-exports/portal-v0.0.3.tar.gz

echo -e "${BLUE}🌍 Test the updated portal at: https://coredeskpro.com/api/modules${NC}"