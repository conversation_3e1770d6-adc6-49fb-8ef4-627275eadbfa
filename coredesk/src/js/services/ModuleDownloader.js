/**
 * ModuleDownloader
 * Service for downloading modules from remote repository in CoreDesk Framework v2.0.0
 * 
 * Features:
 * - Download modules from remote repository
 * - License validation integration
 * - Integrity validation (checksums, signatures)
 * - Progress tracking and cancellation
 * - Retry logic with exponential backoff
 * - Cache-aware downloads
 * - Bandwidth optimization
 */

class ModuleDownloader extends EventTarget {
    constructor(options = {}) {
        super();
        
        // Configuration
        this.config = {
            baseUrl: this.getDefaultBaseUrl(),
            timeout: 30000,                    // 30 seconds
            retries: 3,
            retryDelay: 1000,                  // 1 second
            retryMultiplier: 2,                // Exponential backoff
            maxConcurrentDownloads: 3,
            chunkSize: 1024 * 1024,           // 1MB chunks for large files
            validateChecksums: true,
            validateSignatures: false,         // Requires crypto implementation
            userAgent: 'CoreDesk/2.0.0',
            ...options
        };
        
        // State management
        this.activeDownloads = new Map();     // downloadId -> AbortController
        this.downloadQueue = [];              // Queue for concurrent downloads
        this.downloadStats = new Map();       // downloadId -> stats
        
        // Cache and storage
        this.cache = new Map();               // url -> cached response
        this.cacheTimeout = 5 * 60 * 1000;   // 5 minutes
        
        // Authentication
        this.authToken = null;
        this.authProvider = null;
        
        // Logger
        this.logger = window.GlobalLogger || {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
        
        // Initialize
        this.initialize();
    }

    /**
     * Get default base URL based on environment
     * @private
     */
    getDefaultBaseUrl() {
        // FORCED PRODUCTION URL - Always use production server for module loading
        // This ensures consistent behavior regardless of environment
        console.log('[ModuleDownloader] FORCE PRODUCTION: Using production module repository');
        return 'https://coredeskpro.com/api/modules';
    }

    /**
     * Initialize the downloader
     * @private
     */
    initialize() {
        // Setup authentication if available
        this.setupAuthentication();
        
        // Setup periodic cache cleanup
        this.setupCacheCleanup();
        
        this.logger.info('ModuleDownloader', 'Downloader initialized');
    }

    /**
     * Fetch a module from the repository
     * @param {string} moduleId - Module identifier
     * @param {string} version - Module version ('latest' or specific version)
     * @param {Object} options - Download options
     * @returns {Promise<Object>} Module package data
     */
    async fetchModule(moduleId, version = 'latest', options = {}) {
        const downloadId = `${moduleId}@${version}`;
        
        try {
            this.logger.info('ModuleDownloader', `Fetching module: ${downloadId}`);
            
            // Validate inputs
            this.validateModuleRequest(moduleId, version);
            
            // Check license before downloading
            if (this.config.validateLicense !== false) {
                await this.validateModuleLicense(moduleId);
            }
            
            // Create download session
            const downloadSession = await this.createDownloadSession(downloadId, options);
            
            // Get module metadata first
            const moduleInfo = await this.getModuleInfo(moduleId, version);
            
            // Download module package
            const packageData = await this.downloadModulePackage(moduleInfo, downloadSession);
            
            // Validate package integrity
            if (this.config.validateChecksums) {
                await this.validatePackageIntegrity(packageData, moduleInfo);
            }
            
            // Update download stats
            this.updateDownloadStats(downloadId, 'completed');
            
            this.logger.info('ModuleDownloader', `Module ${downloadId} fetched successfully`);
            this.dispatchEvent(new CustomEvent('moduleDownloaded', {
                detail: { moduleId, version, packageData }
            }));
            
            return packageData;
            
        } catch (error) {
            this.logger.error('ModuleDownloader', `Failed to fetch module ${downloadId}:`, error);
            this.updateDownloadStats(downloadId, 'failed', error);
            
            this.dispatchEvent(new CustomEvent('moduleDownloadError', {
                detail: { moduleId, version, error: error.message }
            }));
            
            throw error;
        } finally {
            // Cleanup download session
            this.cleanupDownloadSession(downloadId);
        }
    }

    /**
     * Get module information/metadata
     * @param {string} moduleId - Module identifier
     * @param {string} version - Module version
     * @returns {Promise<Object>} Module information
     */
    async getModuleInfo(moduleId, version = 'latest') {
        const url = `${this.config.baseUrl}modules/${moduleId}/${version}/info`;
        
        try {
            const response = await this.makeRequest(url, {
                method: 'GET',
                cache: true
            });
            
            return response;
            
        } catch (error) {
            throw new Error(`Failed to get module info for ${moduleId}: ${error.message}`);
        }
    }

    /**
     * Fetch list of available modules
     * @param {Object} filters - Optional filters
     * @returns {Promise<Array>} List of available modules
     */
    async fetchAvailableModules(filters = {}) {
        const url = `${this.config.baseUrl}`;
        const queryParams = new URLSearchParams();
        
        // Add filters as query parameters
        Object.entries(filters).forEach(([key, value]) => {
            if (value) queryParams.append(key, value);
        });
        
        const fullUrl = queryParams.toString() ? `${url}?${queryParams}` : url;
        
        try {
            const response = await this.makeRequest(fullUrl, {
                method: 'GET',
                cache: true,
                cacheTimeout: 10 * 60 * 1000 // 10 minutes for module list
            });
            
            return response.modules || [];
            
        } catch (error) {
            this.logger.error('ModuleDownloader', 'Failed to fetch available modules:', error);
            return [];
        }
    }

    /**
     * Cancel an active download
     * @param {string} downloadId - Download identifier
     * @returns {boolean} True if download was cancelled
     */
    cancelDownload(downloadId) {
        const controller = this.activeDownloads.get(downloadId);
        
        if (controller) {
            controller.abort();
            this.activeDownloads.delete(downloadId);
            this.updateDownloadStats(downloadId, 'cancelled');
            
            this.logger.info('ModuleDownloader', `Download cancelled: ${downloadId}`);
            this.dispatchEvent(new CustomEvent('downloadCancelled', {
                detail: { downloadId }
            }));
            
            return true;
        }
        
        return false;
    }

    /**
     * Get download progress
     * @param {string} downloadId - Download identifier
     * @returns {Object|null} Download progress information
     */
    getDownloadProgress(downloadId) {
        return this.downloadStats.get(downloadId) || null;
    }

    /**
     * Get all active downloads
     * @returns {Array<Object>} Active download information
     */
    getActiveDownloads() {
        return Array.from(this.downloadStats.entries())
            .filter(([_, stats]) => stats.status === 'downloading')
            .map(([downloadId, stats]) => ({ downloadId, ...stats }));
    }

    /**
     * Set authentication token
     * @param {string} token - Authentication token
     */
    setAuthToken(token) {
        this.authToken = token;
        this.logger.debug('ModuleDownloader', 'Authentication token updated');
    }

    /**
     * Set authentication provider
     * @param {Object} provider - Authentication provider
     */
    setAuthProvider(provider) {
        this.authProvider = provider;
        this.logger.debug('ModuleDownloader', 'Authentication provider updated');
    }

    // Private Methods

    /**
     * Validate module request parameters
     * @private
     */
    validateModuleRequest(moduleId, version) {
        if (!moduleId || typeof moduleId !== 'string') {
            throw new Error('Invalid module ID');
        }
        
        if (!version || typeof version !== 'string') {
            throw new Error('Invalid version');
        }
        
        if (!/^[a-z][a-z0-9-_]*$/.test(moduleId)) {
            throw new Error('Module ID format is invalid');
        }
    }

    /**
     * Validate module license
     * @private
     */
    async validateModuleLicense(moduleId) {
        const licenseManager = window.licenseManager;
        
        if (!licenseManager) {
            this.logger.warn('ModuleDownloader', 'License manager not available');
            return; // Skip validation if not available
        }
        
        try {
            // Get module info to check required license
            const moduleInfo = await this.getModuleInfo(moduleId);
            const requiredLicense = moduleInfo.manifest?.requiredLicense;
            
            if (requiredLicense) {
                const isValid = await licenseManager.validateModuleLicense(requiredLicense);
                if (!isValid) {
                    throw new Error(`License required for module ${moduleId}: ${requiredLicense}`);
                }
            }
            
        } catch (error) {
            if (error.message.includes('License required')) {
                throw error;
            }
            // Log other errors but don't fail the download
            this.logger.warn('ModuleDownloader', 'License validation failed:', error);
        }
    }

    /**
     * Create download session
     * @private
     */
    async createDownloadSession(downloadId, options) {
        // Check concurrent download limit
        if (this.activeDownloads.size >= this.config.maxConcurrentDownloads) {
            await this.waitForDownloadSlot();
        }
        
        // Create abort controller for cancellation
        const controller = new AbortController();
        this.activeDownloads.set(downloadId, controller);
        
        // Initialize download stats
        this.downloadStats.set(downloadId, {
            downloadId,
            status: 'starting',
            startTime: Date.now(),
            bytesDownloaded: 0,
            totalBytes: 0,
            progress: 0,
            speed: 0,
            error: null
        });
        
        return {
            downloadId,
            signal: controller.signal,
            options
        };
    }

    /**
     * Download module package
     * @private
     */
    async downloadModulePackage(moduleInfo, downloadSession) {
        const { downloadId, signal } = downloadSession;
        const downloadUrl = moduleInfo.downloadUrl;
        
        if (!downloadUrl) {
            throw new Error('Module download URL not available');
        }
        
        this.updateDownloadStats(downloadId, 'downloading');
        
        try {
            // Download with progress tracking
            const response = await this.downloadWithProgress(downloadUrl, signal, downloadId);
            
            // Parse response based on content type
            const packageData = await this.parseModulePackage(response, moduleInfo);
            
            return packageData;
            
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Download was cancelled');
            }
            throw error;
        }
    }

    /**
     * Download with progress tracking
     * @private
     */
    async downloadWithProgress(url, signal, downloadId) {
        const response = await fetch(url, {
            signal,
            headers: this.getRequestHeaders(),
            timeout: this.config.timeout
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const contentLength = response.headers.get('content-length');
        const totalBytes = contentLength ? parseInt(contentLength, 10) : 0;
        
        this.updateDownloadStats(downloadId, 'downloading', null, { totalBytes });
        
        if (!response.body) {
            // Fallback for browsers without ReadableStream support
            return await response.json();
        }
        
        // Track download progress
        const reader = response.body.getReader();
        const chunks = [];
        let bytesDownloaded = 0;
        const startTime = Date.now();
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                chunks.push(value);
                bytesDownloaded += value.length;
                
                // Update progress
                const progress = totalBytes ? (bytesDownloaded / totalBytes) * 100 : 0;
                const speed = this.calculateSpeed(bytesDownloaded, startTime);
                
                this.updateDownloadStats(downloadId, 'downloading', null, {
                    bytesDownloaded,
                    progress,
                    speed
                });
                
                // Emit progress event
                this.dispatchEvent(new CustomEvent('downloadProgress', {
                    detail: { downloadId, progress, bytesDownloaded, totalBytes, speed }
                }));
            }
            
            // Combine chunks
            const combinedArray = new Uint8Array(bytesDownloaded);
            let offset = 0;
            
            for (const chunk of chunks) {
                combinedArray.set(chunk, offset);
                offset += chunk.length;
            }
            
            // Convert to text and parse JSON
            const text = new TextDecoder().decode(combinedArray);
            return JSON.parse(text);
            
        } finally {
            reader.releaseLock();
        }
    }

    /**
     * Parse module package response
     * @private
     */
    async parseModulePackage(responseData, moduleInfo) {
        // Validate response structure
        if (!responseData.manifest) {
            throw new Error('Invalid module package: missing manifest');
        }
        
        if (!responseData.code) {
            throw new Error('Invalid module package: missing module code');
        }
        
        // Return structured package data
        return {
            manifest: responseData.manifest,
            moduleCode: responseData.code,
            styles: responseData.styles || '',
            assets: responseData.assets || {},
            checksum: responseData.checksum,
            signature: responseData.signature,
            downloadedAt: new Date().toISOString(),
            size: this.calculatePackageSize(responseData)
        };
    }

    /**
     * Validate package integrity
     * @private
     */
    async validatePackageIntegrity(packageData, moduleInfo) {
        // Validate checksum if available
        if (packageData.checksum && moduleInfo.expectedChecksum) {
            if (packageData.checksum !== moduleInfo.expectedChecksum) {
                throw new Error('Package checksum validation failed');
            }
        }
        
        // Validate signature if enabled and available
        if (this.config.validateSignatures && packageData.signature) {
            await this.validatePackageSignature(packageData, moduleInfo);
        }
        
        // Validate manifest integrity
        this.validateManifestIntegrity(packageData.manifest, moduleInfo.manifest);
    }

    /**
     * Validate package signature
     * @private
     */
    async validatePackageSignature(packageData, moduleInfo) {
        // This would require a crypto library implementation
        // For now, just log that signature validation is requested
        this.logger.debug('ModuleDownloader', 'Signature validation requested but not implemented');
    }

    /**
     * Validate manifest integrity
     * @private
     */
    validateManifestIntegrity(packageManifest, infoManifest) {
        const criticalFields = ['id', 'version', 'name'];
        
        for (const field of criticalFields) {
            if (packageManifest[field] !== infoManifest[field]) {
                throw new Error(`Manifest integrity check failed: ${field} mismatch`);
            }
        }
    }

    /**
     * Make HTTP request with retry logic
     * @private
     */
    async makeRequest(url, options = {}) {
        const {
            method = 'GET',
            cache = false,
            cacheTimeout = this.cacheTimeout,
            retries = this.config.retries
        } = options;
        
        // Check cache first
        if (cache) {
            const cachedResponse = this.getCachedResponse(url, cacheTimeout);
            if (cachedResponse) {
                return cachedResponse;
            }
        }
        
        let lastError;
        let delay = this.config.retryDelay;
        
        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                const response = await fetch(url, {
                    method,
                    headers: this.getRequestHeaders(),
                    timeout: this.config.timeout
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // Cache successful responses
                if (cache) {
                    this.setCachedResponse(url, data);
                }
                
                return data;
                
            } catch (error) {
                lastError = error;
                
                // Don't retry on client errors (4xx)
                if (error.message.includes('HTTP 4')) {
                    break;
                }
                
                // Wait before retrying (except on last attempt)
                if (attempt < retries) {
                    await this.sleep(delay);
                    delay *= this.config.retryMultiplier;
                }
            }
        }
        
        throw lastError;
    }

    /**
     * Get request headers
     * @private
     */
    getRequestHeaders() {
        const headers = {
            'User-Agent': this.config.userAgent,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        };
        
        // Add authentication if available
        if (this.authToken) {
            headers['Authorization'] = `Bearer ${this.authToken}`;
        }
        
        return headers;
    }

    /**
     * Get cached response
     * @private
     */
    getCachedResponse(url, timeout) {
        const cached = this.cache.get(url);
        
        if (cached && (Date.now() - cached.timestamp) < timeout) {
            this.logger.debug('ModuleDownloader', `Using cached response for ${url}`);
            return cached.data;
        }
        
        return null;
    }

    /**
     * Set cached response
     * @private
     */
    setCachedResponse(url, data) {
        this.cache.set(url, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * Update download statistics
     * @private
     */
    updateDownloadStats(downloadId, status, error = null, additionalData = {}) {
        const stats = this.downloadStats.get(downloadId);
        
        if (stats) {
            stats.status = status;
            stats.error = error;
            
            if (status === 'completed' || status === 'failed' || status === 'cancelled') {
                stats.endTime = Date.now();
                stats.duration = stats.endTime - stats.startTime;
            }
            
            Object.assign(stats, additionalData);
        }
    }

    /**
     * Calculate download speed
     * @private
     */
    calculateSpeed(bytesDownloaded, startTime) {
        const elapsed = (Date.now() - startTime) / 1000; // seconds
        return elapsed > 0 ? bytesDownloaded / elapsed : 0; // bytes per second
    }

    /**
     * Calculate package size
     * @private
     */
    calculatePackageSize(packageData) {
        return JSON.stringify(packageData).length;
    }

    /**
     * Wait for available download slot
     * @private
     */
    async waitForDownloadSlot() {
        return new Promise((resolve) => {
            const checkSlot = () => {
                if (this.activeDownloads.size < this.config.maxConcurrentDownloads) {
                    resolve();
                } else {
                    setTimeout(checkSlot, 100);
                }
            };
            checkSlot();
        });
    }

    /**
     * Cleanup download session
     * @private
     */
    cleanupDownloadSession(downloadId) {
        this.activeDownloads.delete(downloadId);
        
        // Keep download stats for a while for reference
        setTimeout(() => {
            this.downloadStats.delete(downloadId);
        }, 5 * 60 * 1000); // 5 minutes
    }

    /**
     * Setup authentication
     * @private
     */
    setupAuthentication() {
        // Try to get auth token from license manager
        if (window.licenseManager && typeof window.licenseManager.getAuthToken === 'function') {
            this.authToken = window.licenseManager.getAuthToken();
        }
        
        // Setup auth provider if available
        if (window.authProvider) {
            this.authProvider = window.authProvider;
        }
    }

    /**
     * Setup cache cleanup
     * @private
     */
    setupCacheCleanup() {
        // Clean cache every hour
        setInterval(() => {
            this.cleanupCache();
        }, 60 * 60 * 1000);
    }

    /**
     * Cleanup expired cache entries
     * @private
     */
    cleanupCache() {
        const now = Date.now();
        const expired = [];
        
        for (const [url, cached] of this.cache) {
            if ((now - cached.timestamp) > this.cacheTimeout) {
                expired.push(url);
            }
        }
        
        for (const url of expired) {
            this.cache.delete(url);
        }
        
        if (expired.length > 0) {
            this.logger.debug('ModuleDownloader', `Cleaned up ${expired.length} expired cache entries`);
        }
    }

    /**
     * Sleep utility
     * @private
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Make available globally
window.ModuleDownloader = ModuleDownloader;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModuleDownloader;
}

console.log('ModuleDownloader', '[ModuleDownloader] Service loaded successfully');