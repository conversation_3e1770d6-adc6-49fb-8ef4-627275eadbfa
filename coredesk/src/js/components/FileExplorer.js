/**
 * File Explorer Component
 * Real file system browser for the Explorer panel
 */

class FileExplorer {
    constructor() {
        this.currentPath = null;
        this.history = [];
        this.historyIndex = -1;
        this.bookmarks = [];
        this.showHidden = false;
        this.sortBy = 'name'; // name, size, modified
        this.sortOrder = 'asc';
        
        this.initialize();
    }

    /**
     * Initialize the file explorer
     */
    async initialize() {
        console.log('FileExplorer', 'Initializing file explorer...');
        
        // Set up default path and bookmarks
        this.currentPath = await this.getDefaultPath();
        this.bookmarks = await this.getDefaultBookmarks();
        
        this.setupEventListeners();
        
        console.log('FileExplorer', 'File explorer initialized with path:', this.currentPath);
    }

    /**
     * Get default starting path - prioritize CoreDesk directory
     */
    async getDefaultPath() {
        try {
            // Wait a bit for the main process to be ready
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Try to get CoreDesk directory first
            if (window.electronAPI && window.electronAPI.fileSystem && window.electronAPI.fileSystem.getCoreDeskPath) {
                try {
                    const coreDeskResult = await window.electronAPI.fileSystem.getCoreDeskPath();
                    if (coreDeskResult && coreDeskResult.success) {
                        return coreDeskResult.path;
                    }
                } catch (error) {
                    console.warn('FileExplorer', 'Error getting CoreDesk path:', error);
                }
                
                // Fallback to home directory
                try {
                    const homeResult = await window.electronAPI.fileSystem.getHomeDirectory();
                    if (homeResult && homeResult.success) {
                        return homeResult.path;
                    }
                } catch (error) {
                    console.warn('FileExplorer', 'Error getting home directory:', error);
                }
            }
        } catch (error) {
            console.warn('FileExplorer', 'Could not get real paths, using fallback', error);
        }
        
        // Final fallback paths based on platform detection
        const platform = navigator.platform;
        const userAgent = navigator.userAgent;
        
        if (platform.indexOf('Win') !== -1) {
            return 'C:\\Users\\<USER>\\Documents';
        } else if (platform.indexOf('Mac') !== -1 || userAgent.indexOf('Mac') !== -1) {
            return '/Users/<USER>/Documents';
        } else {
            return '/home/<USER>/Documents';
        }
    }

    /**
     * Get default bookmarks for quick access
     */
    async getDefaultBookmarks() {
        const bookmarks = [];
        
        try {
            if (window.electronAPI && window.electronAPI.fileSystem) {
                // Add CoreDesk directory as first bookmark
                try {
                    if (window.electronAPI.fileSystem.getCoreDeskPath) {
                        const coreDeskResult = await window.electronAPI.fileSystem.getCoreDeskPath();
                        if (coreDeskResult && coreDeskResult.success) {
                            bookmarks.push({ name: 'CoreDesk', path: coreDeskResult.path, icon: '⚙️' });
                        }
                    }
                } catch (error) {
                    console.warn('FileExplorer', 'Error getting CoreDesk path for bookmarks:', error);
                }
                
                // Add home directory
                try {
                    if (window.electronAPI.fileSystem.getHomeDirectory) {
                        const homeResult = await window.electronAPI.fileSystem.getHomeDirectory();
                        if (homeResult && homeResult.success) {
                            bookmarks.push({ name: 'Inicio', path: homeResult.path, icon: '🏠' });
                            
                            // Try to add common subdirectories
                            const commonDirs = [
                                { name: 'Documentos', subdir: 'Documents', icon: '📄' },
                                { name: 'Descargas', subdir: 'Downloads', icon: '📥' },
                                { name: 'Escritorio', subdir: 'Desktop', icon: '🖥️' }
                            ];
                            
                            for (const dir of commonDirs) {
                                try {
                                    const dirPath = this.joinPath(homeResult.path, dir.subdir);
                                    if (window.electronAPI.fileSystem.exists) {
                                        const exists = await window.electronAPI.fileSystem.exists(dirPath);
                                        if (exists && exists.success && exists.exists) {
                                            bookmarks.push({ name: dir.name, path: dirPath, icon: dir.icon });
                                        }
                                    }
                                } catch (error) {
                                    console.warn('FileExplorer', `Error checking directory ${dir.subdir}:`, error);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.warn('FileExplorer', 'Error getting home directory for bookmarks:', error);
                }
            }
        } catch (error) {
            console.warn('FileExplorer', 'Could not get real bookmarks, using fallback', error);
        }
        
        // Fallback bookmarks if API calls failed
        if (bookmarks.length === 0) {
            const platform = navigator.platform;
            const userAgent = navigator.userAgent;
            const isWindows = platform.indexOf('Win') !== -1;
            const isMac = platform.indexOf('Mac') !== -1 || userAgent.indexOf('Mac') !== -1;
            
            if (isWindows) {
                return [
                    { name: 'Documentos', path: 'C:\\Users\\<USER>\\Documents', icon: '📄' },
                    { name: 'Descargas', path: 'C:\\Users\\<USER>\\Downloads', icon: '📥' },
                    { name: 'Escritorio', path: 'C:\\Users\\<USER>\\Desktop', icon: '🖥️' },
                    { name: 'Disco C:', path: 'C:\\', icon: '💾' }
                ];
            } else if (isMac) {
                return [
                    { name: 'Inicio', path: '/Users/<USER>', icon: '🏠' },
                    { name: 'Documentos', path: '/Users/<USER>/Documents', icon: '📄' },
                    { name: 'Descargas', path: '/Users/<USER>/Downloads', icon: '📥' },
                    { name: 'Escritorio', path: '/Users/<USER>/Desktop', icon: '🖥️' },
                    { name: 'Aplicaciones', path: '/Applications', icon: '📦' },
                    { name: 'Raíz', path: '/', icon: '💾' }
                ];
            } else {
                // Linux/Unix
                return [
                    { name: 'Inicio', path: '/home/<USER>', icon: '🏠' },
                    { name: 'Documentos', path: '/home/<USER>/Documents', icon: '📄' },
                    { name: 'Descargas', path: '/home/<USER>/Downloads', icon: '📥' },
                    { name: 'Escritorio', path: '/home/<USER>/Desktop', icon: '🖥️' },
                    { name: 'Raíz', path: '/', icon: '💾' }
                ];
            }
        }
        
        return bookmarks;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for explorer refresh events
        document.addEventListener('explorer:refresh', () => {
            this.refresh();
        });

        // Listen for path navigation events
        document.addEventListener('explorer:navigate', (e) => {
            this.navigateTo(e.detail.path);
        });

        // Setup bookmark and file interaction listeners
        this.setupInteractionListeners();
    }

    /**
     * Setup interaction listeners for bookmarks and files
     */
    setupInteractionListeners() {
        // Use event delegation for dynamic content
        document.addEventListener('click', (e) => {
            // Handle bookmark clicks
            const bookmarkItem = e.target.closest('.bookmark-item');
            if (bookmarkItem) {
                this.handleBookmarkClick(bookmarkItem);
                return;
            }

            // Handle recent file clicks
            const recentFileItem = e.target.closest('.recent-file-item');
            if (recentFileItem) {
                this.handleRecentFileClick(recentFileItem);
                return;
            }

            // Handle file item clicks
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                this.handleFileClick(fileItem);
                return;
            }

            // Handle navigation buttons
            const navBtn = e.target.closest('.nav-btn');
            if (navBtn) {
                this.handleNavButtonClick(navBtn);
                return;
            }
        });
    }

    /**
     * Handle bookmark item clicks
     */
    handleBookmarkClick(bookmarkItem) {
        const path = bookmarkItem.dataset.path;
        const action = bookmarkItem.dataset.action;

        if (action === 'recent') {
            this.showRecentFiles();
        } else if (path) {
            this.navigateTo(path);
        }
    }

    /**
     * Handle recent file item clicks
     */
    handleRecentFileClick(recentFileItem) {
        const fileName = recentFileItem.querySelector('.file-name')?.textContent;
        if (fileName) {
            console.log('FileExplorer', `Opening recent file: ${fileName}`);
            // TODO: Implement recent file opening logic
            this.openFile(fileName);
        }
    }

    /**
     * Handle file item clicks
     */
    handleFileClick(fileItem) {
        const fileName = fileItem.dataset.name;
        const fileType = fileItem.dataset.type;
        
        if (fileType === 'directory') {
            const newPath = this.joinPath(this.currentPath, fileName);
            this.navigateTo(newPath);
        } else {
            this.openFile(fileName);
        }
    }

    /**
     * Handle navigation button clicks
     */
    handleNavButtonClick(navBtn) {
        if (navBtn.disabled) return;

        if (navBtn.classList.contains('back-btn')) {
            this.goBack();
        } else if (navBtn.classList.contains('forward-btn')) {
            this.goForward();
        } else if (navBtn.classList.contains('up-btn')) {
            this.goUp();
        } else if (navBtn.classList.contains('refresh-btn')) {
            this.refresh();
        }
    }

    /**
     * Show recent files (placeholder implementation)
     */
    showRecentFiles() {
        console.log('FileExplorer', 'Showing recent files...');
        // TODO: Implement recent files view
    }

    /**
     * Open a file (placeholder implementation)
     */
    openFile(fileName) {
        console.log('FileExplorer', `Opening file: ${fileName}`);
        // TODO: Implement file opening logic
    }

    /**
     * Generate the complete explorer content
     */
    generateExplorerContent() {
        return `
            <div class="file-explorer-container">
                <!-- Navigation Bar -->
                <div class="explorer-nav">
                    <div class="nav-buttons">
                        <button class="nav-btn back-btn" title="Atrás" ${this.canGoBack() ? '' : 'disabled'}>
                            ←
                        </button>
                        <button class="nav-btn forward-btn" title="Adelante" ${this.canGoForward() ? '' : 'disabled'}>
                            →
                        </button>
                        <button class="nav-btn up-btn" title="Subir" ${this.canGoUp() ? '' : 'disabled'}>
                            ↑
                        </button>
                        <button class="nav-btn refresh-btn" title="Actualizar">
                            🔄
                        </button>
                    </div>
                    <div class="address-bar">
                        <input type="text" class="path-input" value="${this.currentPath}" placeholder="Ruta del directorio">
                        <button class="go-btn" title="Ir">→</button>
                    </div>
                </div>

                <!-- Content Layout -->
                <div class="explorer-content">
                    <!-- Sidebar with bookmarks -->
                    <div class="explorer-sidebar">
                        <div class="bookmarks-section">
                            <h5>Accesos Rápidos</h5>
                            ${this.generateBookmarks()}
                        </div>
                    </div>

                    <!-- Main file area -->
                    <div class="explorer-main">
                        <!-- Toolbar -->
                        <div class="explorer-toolbar">
                            <div class="view-options">
                                <button class="view-btn list-view active" title="Vista de lista">≡</button>
                                <button class="view-btn grid-view" title="Vista de cuadrícula">▦</button>
                            </div>
                            <div class="sort-options">
                                <select class="sort-select">
                                    <option value="name" ${this.sortBy === 'name' ? 'selected' : ''}>Nombre</option>
                                    <option value="size" ${this.sortBy === 'size' ? 'selected' : ''}>Tamaño</option>
                                    <option value="modified" ${this.sortBy === 'modified' ? 'selected' : ''}>Modificado</option>
                                </select>
                                <button class="sort-order-btn" title="Orden ${this.sortOrder === 'asc' ? 'descendente' : 'ascendente'}">
                                    ${this.sortOrder === 'asc' ? '↑' : '↓'}
                                </button>
                            </div>
                            <div class="options">
                                <label class="hidden-toggle">
                                    <input type="checkbox" ${this.showHidden ? 'checked' : ''}> 
                                    Mostrar ocultos
                                </label>
                            </div>
                        </div>

                        <!-- File listing -->
                        <div class="file-listing">
                            ${this.generateFileList()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate bookmarks sidebar with improved design
     */
    generateBookmarks() {
        // Create a more comprehensive and useful quick access section
        const quickAccessItems = [
            ...this.bookmarks
        ];

        // Only add project shortcuts if we have a valid current path
        if (this.currentPath) {
            quickAccessItems.push(
                { name: 'Proyectos', path: this.joinPath(this.currentPath, 'proyectos'), icon: '📁' },
                { name: 'Módulos', path: this.joinPath(this.currentPath, 'modulos'), icon: '📦' },
                { name: 'Configuración', path: this.joinPath(this.currentPath, 'config'), icon: '⚙️' }
            );
        }

        // Always add the recent files action
        quickAccessItems.push({ name: 'Recientes', path: null, icon: '🕒', action: 'recent' });

        const bookmarkHTML = quickAccessItems.map(item => `
            <div class="bookmark-item ${item.action ? 'bookmark-action' : ''}" data-path="${item.path || ''}" data-action="${item.action || ''}">
                <span class="bookmark-icon">${item.icon}</span>
                <span class="bookmark-name">${item.name}</span>
            </div>
        `).join('');

        // Add a compact recent files section
        return `
            ${bookmarkHTML}
            <div class="bookmarks-separator"></div>
            <div class="recent-files-section">
                <h6>Archivos Recientes</h6>
                <div class="recent-file-item">
                    <span class="file-icon">📄</span>
                    <span class="file-name">documento.txt</span>
                </div>
                <div class="recent-file-item">
                    <span class="file-icon">📊</span>
                    <span class="file-name">informe.xlsx</span>
                </div>
                <div class="recent-file-item">
                    <span class="file-icon">🎨</span>
                    <span class="file-name">diseño.pdf</span>
                </div>
            </div>
        `;
    }

    /**
     * Generate file list using real file system APIs
     */
    generateFileList() {
        if (window.electronAPI && window.electronAPI.fileSystem) {
            return this.generateRealFileList();
        } else {
            return this.generateDemoFileList();
        }
    }

    /**
     * Generate real file list using electron API
     */
    generateRealFileList() {
        // Since this is called from a sync context but file operations are async,
        // we need to handle this differently
        this.loadRealFileList();
        return '<div class="explorer-loading"><div class="loading-spinner"></div><p>Cargando archivos...</p></div>';
    }

    /**
     * Load real file list asynchronously
     */
    async loadRealFileList() {
        try {
            if (!window.electronAPI || !window.electronAPI.fileSystem || !window.electronAPI.fileSystem.readDirectory) {
                this.showErrorInDOM('Sistema de archivos no disponible');
                return;
            }

            const result = await window.electronAPI.fileSystem.readDirectory(this.currentPath);
            
            if (result && result.success) {
                // Prepare files for rendering
                const files = [];
                
                // Add parent directory if not at root
                if (this.canGoUp()) {
                    files.push({ name: '..', type: 'directory', isParent: true });
                }
                
                // Add files and directories from result
                if (result.items && Array.isArray(result.items)) {
                    result.items.forEach(item => {
                        files.push({
                            name: item.name,
                            type: item.type,
                            isDirectory: item.isDirectory,
                            isFile: item.isFile,
                            size: item.size,
                            modified: item.modified,
                            extension: item.extension
                        });
                    });
                }
                
                // Update the file listing in the DOM
                this.updateFileListingInDOM(files);
            } else {
                this.showErrorInDOM('Error al leer el directorio: ' + (result?.error || 'Error desconocido'));
            }
        } catch (error) {
            console.error('FileExplorer', 'Error loading file list:', error);
            this.showErrorInDOM('Error al acceder al directorio: ' + error.message);
        }
    }

    /**
     * Update file listing in DOM directly
     */
    updateFileListingInDOM(files) {
        const fileListingElement = document.querySelector('.file-listing');
        if (fileListingElement) {
            fileListingElement.innerHTML = this.renderFileList(files);
        }
    }

    /**
     * Show error message in DOM
     */
    showErrorInDOM(message) {
        const fileListingElement = document.querySelector('.file-listing');
        if (fileListingElement) {
            fileListingElement.innerHTML = this.generateErrorMessage(message);
        }
    }

    /**
     * Generate demo file list for demonstration
     */
    generateDemoFileList() {
        // Generate realistic demo content based on current path
        const platform = navigator.platform;
        const isWindows = platform.indexOf('Win') !== -1;
        const path = this.currentPath;
        
        let demoFiles = [];
        
        // Add parent directory if not at root
        if (this.canGoUp()) {
            demoFiles.push({ name: '..', type: 'directory', isParent: true });
        }
        
        // Generate path-specific content
        if (path.includes('Documents') || path.includes('Documentos')) {
            demoFiles = demoFiles.concat([
                { name: 'Proyectos', type: 'directory', size: null, modified: '2024-07-01' },
                { name: 'Contratos', type: 'directory', size: null, modified: '2024-06-25' },
                { name: 'Reportes', type: 'directory', size: null, modified: '2024-06-20' },
                { name: 'caso_001.pdf', type: 'file', size: 2456789, modified: '2024-07-01', extension: 'pdf' },
                { name: 'contrato_servicios.docx', type: 'file', size: 1234567, modified: '2024-06-30', extension: 'docx' },
                { name: 'informe_mensual.xlsx', type: 'file', size: 987654, modified: '2024-06-28', extension: 'xlsx' },
                { name: 'presupuesto_2024.pdf', type: 'file', size: 3456789, modified: '2024-06-25', extension: 'pdf' },
                { name: 'notas.txt', type: 'file', size: 1024, modified: '2024-06-20', extension: 'txt' }
            ]);
        } else if (path.includes('Downloads') || path.includes('Descargas')) {
            demoFiles = demoFiles.concat([
                { name: 'CoreDesk_Setup.exe', type: 'file', size: 45678900, modified: '2024-07-01', extension: 'exe' },
                { name: 'manual_usuario.pdf', type: 'file', size: 3456789, modified: '2024-06-30', extension: 'pdf' },
                { name: 'imagen_perfil.png', type: 'file', size: 234567, modified: '2024-06-28', extension: 'png' },
                { name: 'backup_datos.zip', type: 'file', size: 12345678, modified: '2024-06-25', extension: 'zip' },
                { name: 'presentacion.pptx', type: 'file', size: 5678901, modified: '2024-06-22', extension: 'pptx' }
            ]);
        } else if (path.includes('Desktop') || path.includes('Escritorio')) {
            demoFiles = demoFiles.concat([
                { name: 'CoreDesk.lnk', type: 'file', size: 2048, modified: '2024-07-01', extension: 'lnk' },
                { name: 'Notas Rápidas.txt', type: 'file', size: 512, modified: '2024-06-30', extension: 'txt' },
                { name: 'Proyecto_Actual', type: 'directory', size: null, modified: '2024-06-28' },
                { name: 'Backup', type: 'directory', size: null, modified: '2024-06-25' }
            ]);
        } else if (isWindows && path === 'C:\\') {
            demoFiles = demoFiles.concat([
                { name: 'Program Files', type: 'directory', size: null, modified: '2024-06-01' },
                { name: 'Program Files (x86)', type: 'directory', size: null, modified: '2024-06-01' },
                { name: 'Windows', type: 'directory', size: null, modified: '2024-06-01' },
                { name: 'Users', type: 'directory', size: null, modified: '2024-06-15' },
                { name: 'PerfLogs', type: 'directory', size: null, modified: '2024-05-01' }
            ]);
        } else if (!isWindows && path === '/') {
            demoFiles = demoFiles.concat([
                { name: 'bin', type: 'directory', size: null, modified: '2024-06-01' },
                { name: 'etc', type: 'directory', size: null, modified: '2024-06-15' },
                { name: 'home', type: 'directory', size: null, modified: '2024-06-20' },
                { name: 'usr', type: 'directory', size: null, modified: '2024-06-01' },
                { name: 'var', type: 'directory', size: null, modified: '2024-06-10' },
                { name: 'opt', type: 'directory', size: null, modified: '2024-05-15' }
            ]);
        } else {
            // Default generic content
            demoFiles = demoFiles.concat([
                { name: 'Documentos', type: 'directory', size: null, modified: '2024-07-01' },
                { name: 'Descargas', type: 'directory', size: null, modified: '2024-06-30' },
                { name: 'Escritorio', type: 'directory', size: null, modified: '2024-06-28' },
                { name: 'Imágenes', type: 'directory', size: null, modified: '2024-06-25' },
                { name: 'archivo_ejemplo.txt', type: 'file', size: 1024, modified: '2024-06-20', extension: 'txt' }
            ]);
        }

        return this.renderFileList(demoFiles);
    }

    /**
     * Render file list HTML
     */
    renderFileList(files) {
        if (!files || files.length === 0) {
            return '<div class="empty-directory">Esta carpeta está vacía</div>';
        }

        return files.map(file => {
            const icon = this.getFileIcon(file);
            const sizeStr = file.type === 'file' ? this.formatFileSize(file.size) : '';
            const modifiedStr = this.formatDate(file.modified);

            return `
                <div class="file-item ${file.type}" data-path="${file.name}" ${file.type === 'file' ? `data-extension="${file.extension || ''}"` : ''}>
                    <div class="file-icon">${icon}</div>
                    <div class="file-name" title="${file.name}">${file.name}</div>
                    <div class="file-size">${sizeStr}</div>
                    <div class="file-modified">${modifiedStr}</div>
                </div>
            `;
        }).join('');
    }

    /**
     * Get appropriate icon for file/folder
     */
    getFileIcon(file) {
        if (file.isParent) return '↰';
        if (file.type === 'directory') return '📁';
        
        // File icons based on extension
        const extension = file.extension?.toLowerCase();
        const iconMap = {
            'pdf': '📄',
            'doc': '📝', 'docx': '📝',
            'xls': '📊', 'xlsx': '📊',
            'ppt': '📊', 'pptx': '📊',
            'txt': '📝', 'md': '📝',
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
            'mp4': '🎬', 'avi': '🎬', 'mov': '🎬',
            'mp3': '🎵', 'wav': '🎵', 'flac': '🎵',
            'zip': '📦', 'rar': '📦', '7z': '📦',
            'exe': '⚙️', 'msi': '⚙️',
            'js': '📜', 'html': '🌐', 'css': '🎨',
            'json': '📋', 'xml': '📋'
        };
        
        return iconMap[extension] || '📄';
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (!bytes) return '';
        
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    /**
     * Format date for display
     */
    formatDate(dateStr) {
        if (!dateStr) return '';
        
        try {
            const date = new Date(dateStr);
            return date.toLocaleDateString('es-ES');
        } catch (error) {
            return dateStr;
        }
    }

    /**
     * Generate error message
     */
    generateErrorMessage(message) {
        return `
            <div class="explorer-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
                <button class="retry-btn">Reintentar</button>
            </div>
        `;
    }

    /**
     * Navigation helper methods
     */
    canGoBack() {
        return this.historyIndex > 0;
    }

    canGoForward() {
        return this.historyIndex < this.history.length - 1;
    }

    canGoUp() {
        // Check if currentPath is null or undefined
        if (!this.currentPath) {
            return false;
        }
        
        const isWindows = navigator.platform.indexOf('Win') !== -1;
        if (isWindows) {
            return this.currentPath !== 'C:\\' && this.currentPath.length > 3;
        } else {
            return this.currentPath !== '/';
        }
    }

    /**
     * Navigate to a specific path
     */
    navigateTo(path) {
        // Add to history
        this.history = this.history.slice(0, this.historyIndex + 1);
        this.history.push(path);
        this.historyIndex = this.history.length - 1;
        
        this.currentPath = path;
        this.refresh();
    }

    /**
     * Refresh the current directory
     */
    refresh() {
        // Reload the file list for the current directory
        if (window.electronAPI && window.electronAPI.fileSystem) {
            this.loadRealFileList();
        }
        
        // Also trigger panel update if needed
        if (window.panelManager) {
            window.panelManager.updatePanelContent('left');
        }
    }

    /**
     * Get the current explorer state
     */
    getState() {
        return {
            currentPath: this.currentPath,
            showHidden: this.showHidden,
            sortBy: this.sortBy,
            sortOrder: this.sortOrder
        };
    }

    /**
     * Helper method to join paths correctly
     */
    joinPath(basePath, fileName) {
        // Handle null or undefined basePath
        if (!basePath || !fileName) {
            return basePath || fileName || '';
        }
        
        const isWindows = navigator.platform.indexOf('Win') !== -1;
        const separator = isWindows ? '\\' : '/';
        
        if (basePath.endsWith(separator)) {
            return basePath + fileName;
        } else {
            return basePath + separator + fileName;
        }
    }
}

// Create global instance
window.fileExplorer = new FileExplorer();
console.log('FileExplorer', 'File explorer initialized');