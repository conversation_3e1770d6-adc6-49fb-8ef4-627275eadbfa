/**
 * SecurityManager.js
 * Comprehensive security management system for CoreDesk Framework
 * Handles CSP, input validation, XSS prevention, and security policies
 * Enhanced with encryption, audit logging, and advanced threat detection
 */

// Dependencies will be available globally
// const { errorHandler } = require('../utils/ErrorHandler');
// const { inputValidator } = require('../utils/InputValidator');

class SecurityManager {
    constructor() {
        // Initialize dependencies lazily to avoid circular dependencies
        this.errorHandler = null;
        this.inputValidator = null;
        
        this.securityPolicies = {
            csp: {
                'default-src': ["'self'"],
                'script-src': ["'self'", "'unsafe-eval'"],
                'style-src': ["'self'", "'unsafe-inline'"],
                'img-src': ["'self'", "data:", "https:"],
                'font-src': ["'self'", "data:"],
                'connect-src': ["'self'", "https://api.coredeskpro.com", "https://coredeskpro.com", "https://*.coredeskpro.com", "http://coredeskpro.com", "http://*.coredeskpro.com", "http://localhost:*", "https://localhost:*"],
                'media-src': ["'none'"],
                'object-src': ["'none'"],
                'frame-src': ["'none'"],
                'worker-src': ["'none'"],
                'frame-ancestors': ["'none'"],
                'form-action': ["'self'"],
                'base-uri': ["'self'"]
            },
            trustedDomains: [
                'coredeskpro.com',
                'api.coredeskpro.com',
                'portal.coredeskpro.com',
                'localhost'
            ],
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedFileTypes: [
                '.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', 
                '.ppt', '.pptx', '.jpg', '.jpeg', '.png', '.gif', '.svg'
            ],
            sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
            maxLoginAttempts: 5,
            lockoutDuration: 15 * 60 * 1000 // 15 minutes
        };

        this.securityEvents = [];
        this.loginAttempts = new Map();
        this.blockedIPs = new Map();
        this.sessionRegistry = new Map();
        this.cryptoKey = null;
        this.auditLog = [];
        
        // Enhanced security monitoring
        this.threatDetection = {
            suspiciousPatterns: new Map(),
            behaviorAnalysis: new Map(),
            anomalyThreshold: 5
        };
        
        // Don't auto-initialize - this will be handled by GlobalInit
        // this.initialize();
    }

    /**
     * Initialize security manager
     */
    initialize() {
        console.log('Security', '[SecurityManager] Initializing security policies...', );
        
        this.setupCSP();
        this.setupEventListeners();
        this.startSecurityMonitoring();
        
        console.log('Security', '[SecurityManager] Security manager initialized successfully', );
    }

    /**
     * Initialize dependencies after all components are loaded
     */
    initializeDependencies() {
        if (!this.errorHandler && window.errorHandler) {
            this.errorHandler = window.errorHandler;
        }
        if (!this.inputValidator && window.inputValidator) {
            this.inputValidator = window.inputValidator;
        }
    }

    /**
     * Safely handle errors through ErrorHandler if available
     */
    safeHandleError(error, context, metadata = {}) {
        // Try to initialize dependencies if not done yet
        this.initializeDependencies();
        
        if (this.errorHandler) {
            return this.errorHandler.handleError(error, context, metadata);
        }
        // Fallback to console logging if ErrorHandler not available
        console.error(`[SecurityManager] ${context}:`, error, metadata);
        return null;
    }

    /**
     * Setup Content Security Policy
     */
    setupCSP() {
        try {
            const cspString = Object.entries(this.securityPolicies.csp)
                .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
                .join('; ');

            // Set CSP via meta tag (for renderer process)
            const existingCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
            if (!existingCSP) {
                const cspMeta = document.createElement('meta');
                cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
                cspMeta.setAttribute('content', cspString);
                document.head.appendChild(cspMeta);
            }

            console.log('Security', '[SecurityManager] CSP configured successfully', );
        } catch (error) {
            console.error('Security', '[SecurityManager] Failed to setup CSP:', error);
        }
    }

    /**
     * Setup security event listeners
     */
    setupEventListeners() {
        // Monitor for potential XSS attempts using modern MutationObserver
        if (typeof MutationObserver !== 'undefined') {
            this.domObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                this.scanForXSS(node);
                            }
                        });
                    }
                });
            });
            
            this.domObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
        } else {
            // Fallback for older browsers (should not be needed in Electron)
            document.addEventListener('DOMNodeInserted', (event) => {
                this.scanForXSS(event.target);
            });
        }

        // Monitor for suspicious activities
        window.addEventListener('error', (event) => {
            this.handleSecurityError(event);
        });

        // Monitor for unauthorized external requests
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = (url, options) => {
                if (!this.isAllowedRequest(url)) {
                    this.logSecurityEvent('blocked_request', { url, reason: 'Unauthorized domain' });
                    return Promise.reject(new Error('Request blocked by security policy'));
                }
                return originalFetch(url, options);
            };
        }
    }

    /**
     * Start security monitoring
     */
    startSecurityMonitoring() {
        // Periodic security checks
        setInterval(() => {
            this.performSecurityScan();
        }, 5 * 60 * 1000); // Every 5 minutes

        // Clean up old security events
        setInterval(() => {
            this.cleanupSecurityEvents();
        }, 60 * 60 * 1000); // Every hour
    }

    /**
     * Enhanced input validation with security monitoring
     */
    validateInput(input, type, options = {}) {
        try {
            // Use the enhanced InputValidator instance
            const validator = this.inputValidator || (typeof window !== 'undefined' ? window.inputValidator : null);
            
            if (!validator) {
                this.safeHandleError(
                    new Error('InputValidator not available'),
                    'validation-service-unavailable'
                );
                return { valid: false, error: 'Validation service unavailable' };
            }

            let result;
            
            switch (type) {
                case 'email':
                    result = validator.validateEmail(input);
                    break;
                case 'password':
                    result = validator.validatePassword(input, options);
                    break;
                case 'licenseKey':
                    result = validator.validateLicenseKey(input);
                    break;
                case 'verificationCode':
                    result = validator.validateVerificationCode(input);
                    break;
                case 'fileName':
                    result = validator.validateFileName(input);
                    break;
                case 'url':
                    result = validator.validateUrl(input, options);
                    break;
                case 'number':
                    result = validator.validateNumber(input, options.min, options.max);
                    break;
                case 'boolean':
                    result = validator.validateBoolean(input);
                    break;
                case 'json':
                    result = validator.validateJson(input);
                    break;
                case 'file':
                    result = validator.validateFileUpload(input, options);
                    break;
                case 'apiKey':
                    result = validator.validateApiKey(input, options);
                    break;
                default:
                    result = validator.validateText(input, options);
            }

            if (!result.valid) {
                this.logSecurityEvent('validation_failed', { 
                    type, 
                    error: result.error || result.errors,
                    input: this.maskSensitiveData(input, type),
                    identifier: options.identifier
                });
            }

            return result;
        } catch (error) {
            this.safeHandleError(error, 'input-validation', { type, hasInput: !!input });
            return { valid: false, error: 'Validation failed' };
        }
    }

    /**
     * Check if request URL is allowed
     */
    isAllowedRequest(url) {
        try {
            // Allow relative URLs for local resources
            if (!url.includes('://')) {
                // This is a relative URL, allow it for local resources
                // Check if it's a safe local path (CSS, JS, images, etc.)
                const safePaths = [
                    'css/', 'js/', 'assets/', 'src/', 'images/', 'fonts/', 'packages/',
                    '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'
                ];
                
                if (safePaths.some(path => url.includes(path))) {
                    return true;
                }
            }
            
            const urlObj = new URL(url);
            
            // Allow localhost for development
            if (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1') {
                return true;
            }

            // Check against trusted domains
            const isAllowed = this.securityPolicies.trustedDomains.some(domain => 
                urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
            );
            
            console.log(`[SecurityManager] URL check: ${url} -> hostname: ${urlObj.hostname} -> allowed: ${isAllowed}`);
            console.log(`[SecurityManager] Trusted domains:`, this.securityPolicies.trustedDomains);
            
            return isAllowed;
        } catch (error) {
            // If URL parsing fails, it might be a relative URL, check if it's safe
            const safePaths = [
                'css/', 'js/', 'assets/', 'src/', 'images/', 'fonts/',
                '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'
            ];
            
            return safePaths.some(path => url.includes(path));
        }
    }

    /**
     * Validate file upload
     */
    validateFileUpload(file) {
        const errors = [];

        // Check file size
        if (file.size > this.securityPolicies.maxFileSize) {
            errors.push(`File size exceeds limit of ${this.securityPolicies.maxFileSize / 1024 / 1024}MB`);
        }

        // Check file type
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (!this.securityPolicies.allowedFileTypes.includes(extension)) {
            errors.push(`File type ${extension} is not allowed`);
        }

        // Check for suspicious file names
        this.initializeDependencies();
        if (this.inputValidator && this.inputValidator.containsDangerousContent(file.name)) {
            errors.push('File name contains dangerous characters');
        }

        const isValid = errors.length === 0;
        
        if (!isValid) {
            this.logSecurityEvent('file_validation_failed', {
                fileName: file.name,
                fileSize: file.size,
                errors
            });
        }

        return {
            valid: isValid,
            errors,
            sanitizedName: isValid && this.inputValidator ? this.inputValidator.sanitizeText(file.name) : null
        };
    }

    /**
     * Enhanced login attempt tracking with brute force protection
     */
    trackLoginAttempt(identifier, success, metadata = {}) {
        try {
            // First validate the login attempt using InputValidator
            const validator = this.inputValidator || (typeof window !== 'undefined' ? window.inputValidator : null);
            
            if (validator) {
                const attemptValidation = validator.validateLoginAttempt(identifier);
                if (!attemptValidation.valid) {
                    return attemptValidation; // Return lockout info
                }
            }
            
            const now = Date.now();
            
            if (!this.loginAttempts.has(identifier)) {
                this.loginAttempts.set(identifier, []);
            }

            const attempts = this.loginAttempts.get(identifier);
            
            if (success) {
                // Clear attempts on successful login
                this.loginAttempts.delete(identifier);
                
                // Clear InputValidator failed attempts too
                if (validator && validator.clearFailedAttempts) {
                    validator.clearFailedAttempts(identifier);
                }
                
                this.logSecurityEvent('login_success', { 
                    identifier: this.maskSensitiveData(identifier, 'email'),
                    metadata 
                });
            } else {
                // Add failed attempt
                attempts.push(now);
                
                // Record in InputValidator too
                if (validator && validator.recordFailedLogin) {
                    validator.recordFailedLogin(identifier);
                }
                
                // Remove attempts older than lockout duration
                const cutoff = now - this.securityPolicies.lockoutDuration;
                const recentAttempts = attempts.filter(time => time > cutoff);
                this.loginAttempts.set(identifier, recentAttempts);
                
                this.logSecurityEvent('login_failed', { 
                    identifier: this.maskSensitiveData(identifier, 'email'),
                    attemptCount: recentAttempts.length,
                    metadata
                });
                
                // Check if account should be locked
                if (recentAttempts.length >= this.securityPolicies.maxLoginAttempts) {
                    this.lockAccount(identifier);
                    return { 
                        locked: true, 
                        retryAfter: this.securityPolicies.lockoutDuration,
                        remainingTime: Math.ceil(this.securityPolicies.lockoutDuration / 1000 / 60)
                    };
                }
            }

            return { locked: false };
        } catch (error) {
            this.safeHandleError(error, 'login-attempt-tracking', { identifier: this.maskSensitiveData(identifier, 'email') });
            return { locked: false, error: 'Login tracking failed' };
        }
    }

    /**
     * Check if account is currently locked
     */
    isAccountLocked(identifier) {
        const attempts = this.loginAttempts.get(identifier);
        if (!attempts || attempts.length === 0) {
            return false;
        }

        const now = Date.now();
        const cutoff = now - this.securityPolicies.lockoutDuration;
        const recentAttempts = attempts.filter(time => time > cutoff);
        
        return recentAttempts.length >= this.securityPolicies.maxLoginAttempts;
    }

    /**
     * Lock account due to too many failed attempts
     */
    lockAccount(identifier) {
        this.logSecurityEvent('account_locked', { 
            identifier,
            lockDuration: this.securityPolicies.lockoutDuration
        });
        
        console.warn('Security', `[SecurityManager] Account locked: ${identifier}`, );
    }

    /**
     * Scan for XSS attempts in DOM nodes
     */
    scanForXSS(node) {
        if (!node || node.nodeType !== Node.ELEMENT_NODE) {
            return;
        }

        // Check for dangerous attributes
        const dangerousAttributes = ['onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onfocus', 'onblur'];
        dangerousAttributes.forEach(attr => {
            if (node.hasAttribute(attr)) {
                const attrValue = node.getAttribute(attr);
                
                // Only flag if the attribute contains potentially malicious code
                if (this.isAttributeValueMalicious(attrValue)) {
                    this.logSecurityEvent('xss_attempt', {
                        tagName: node.tagName,
                        attribute: attr,
                        value: attrValue,
                        reason: 'Dangerous event handler detected'
                    });
                    
                    // Remove the dangerous attribute
                    node.removeAttribute(attr);
                }
            }
        });

        // Check for actual XSS threats (not just any HTML content)
        this.initializeDependencies();
        if (node.innerHTML && this.inputValidator) {
            // Skip XSS scanning for certain legitimate elements
            if (this.isElementExemptFromXSSScanning(node)) {
                return;
            }
            
            // First check if it's legitimate HTML content
            const isLegitimate = this.inputValidator.isLegitimateHTML(node.innerHTML) || 
                               this.inputValidator.isLegitimateHTMLComment(node.innerHTML);
            
            // Only flag as XSS if it contains actual threats and isn't legitimate HTML
            if (!isLegitimate && this.inputValidator.containsXSSThreats(node.innerHTML)) {
                this.logSecurityEvent('xss_attempt', {
                    tagName: node.tagName,
                    content: node.innerHTML.substring(0, 100),
                    reason: 'Contains actual XSS threats',
                    elementId: node.id || 'unknown',
                    className: node.className || 'none'
                });
                
                // Sanitize the content
                node.innerHTML = this.inputValidator.sanitizeHtml(node.innerHTML);
            }
        }
    }

    /**
     * Check if an attribute value contains malicious code
     */
    isAttributeValueMalicious(value) {
        if (!value || typeof value !== 'string') {
            return false;
        }

        const maliciousPatterns = [
            /javascript\s*:/gi,
            /vbscript\s*:/gi,
            /data\s*:\s*text\/html/gi,
            /alert\s*\(/gi,
            /eval\s*\(/gi,
            /document\./gi,
            /window\./gi,
            /location\./gi
        ];

        return maliciousPatterns.some(pattern => pattern.test(value));
    }

    /**
     * Check if an element should be exempt from XSS scanning
     */
    isElementExemptFromXSSScanning(node) {
        // Exempt certain application-specific elements that are known to be safe
        const exemptIds = [
            'sync-status-panel', 'license-modal', 'explorer-nav', 
            'main-content', 'activity-bar', 'title-bar'
        ];
        
        const exemptClasses = [
            'sync-panel-content', 'modal-content', 'explorer-nav',
            'navigation-content', 'application-content'
        ];

        return exemptIds.includes(node.id) || 
               exemptClasses.some(cls => node.classList?.contains(cls)) ||
               node.hasAttribute('data-safe-html');
    }

    /**
     * Handle security-related errors
     */
    handleSecurityError(event) {
        if (event.message && (
            event.message.includes('script') ||
            event.message.includes('eval') ||
            event.message.includes('unsafe')
        )) {
            this.logSecurityEvent('security_error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        }
    }

    /**
     * Perform periodic security scan
     */
    performSecurityScan() {
        try {
            this.initializeDependencies();
            // Check for suspicious DOM modifications
            const scripts = document.querySelectorAll('script');
            scripts.forEach(script => {
                if (script.src && !this.isAllowedRequest(script.src)) {
                    // Don't flag legitimate module package scripts
                    if (script.src.includes('/packages/') && 
                        (script.src.includes('/install.js') || script.src.includes('Package.js'))) {
                        console.log('Security', '[SecurityManager] Allowing legitimate module script:', script.src);
                        return;
                    }
                    this.logSecurityEvent('suspicious_script', { src: script.src });
                    script.remove();
                }
            });

            // Check for inline styles with dangerous content
            const elements = document.querySelectorAll('[style]');
            elements.forEach(element => {
                const style = element.getAttribute('style');
                if (style && this.inputValidator && this.inputValidator.containsDangerousContent(style)) {
                    this.logSecurityEvent('suspicious_style', { style });
                    element.removeAttribute('style');
                }
            });

            console.log('Security', '[SecurityManager] Periodic security scan completed', );
        } catch (error) {
            console.error('Security', '[SecurityManager] Security scan error:', error);
        }
    }

    /**
     * Enhanced security event logging with ErrorHandler integration
     */
    logSecurityEvent(type, details = {}) {
        const event = {
            type,
            timestamp: new Date().toISOString(),
            details,
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js',
            url: typeof window !== 'undefined' ? window.location.href : 'N/A',
            severity: this.getSecurityEventSeverity(type)
        };

        this.securityEvents.push(event);
        this.auditLog.push(event);
        
        // Keep audit log manageable
        if (this.auditLog.length > 1000) {
            this.auditLog.shift();
        }
        
        // Use ErrorHandler for high-severity events
        if (event.severity === 'high') {
            this.safeHandleError(
                new Error(`Security event: ${type}`),
                'security-event',
                { eventType: type, details }
            );
        }
        
        // Enhanced threat detection - skip pattern analysis for internal security system events
        // to prevent infinite loops when security system responds to threats
        const internalSecurityEvents = ['security_anomaly', 'security_lockdown', 'security_scan'];
        if (!internalSecurityEvents.includes(type)) {
            this.analyzeSecurityPattern(type, details);
        }
        
        // Log to console with appropriate level
        const logLevel = event.severity === 'high' ? 'error' : 
                        event.severity === 'medium' ? 'warn' : 'info';
        console[logLevel]('Security', `[SecurityEvent] ${type}:`, details);

        // Emit event for external monitoring
        if (typeof window !== 'undefined' && window.CoreDeskEvents) {
            window.CoreDeskEvents.emit('security-event', event);
        }
    }
    
    /**
     * Get security event severity level
     */
    getSecurityEventSeverity(type) {
        const highSeverityEvents = [
            'xss_attempt', 'security_error', 'account_locked', 'suspicious_script',
            'malware_detected', 'data_breach_attempt', 'privilege_escalation'
        ];
        
        const mediumSeverityEvents = [
            'login_failed', 'validation_failed', 'file_validation_failed',
            'blocked_request', 'suspicious_style', 'rate_limit_exceeded'
        ];
        
        if (highSeverityEvents.includes(type)) return 'high';
        if (mediumSeverityEvents.includes(type)) return 'medium';
        return 'low';
    }
    
    /**
     * Analyze security patterns for threat detection
     */
    analyzeSecurityPattern(type, details) {
        try {
            const key = `${type}_${details.identifier || 'unknown'}`;
            const now = Date.now();
            
            if (!this.threatDetection.suspiciousPatterns.has(key)) {
                this.threatDetection.suspiciousPatterns.set(key, []);
            }
            
            const patterns = this.threatDetection.suspiciousPatterns.get(key);
            patterns.push(now);
            
            // Remove patterns older than 10 minutes
            const cutoff = now - (10 * 60 * 1000);
            const recentPatterns = patterns.filter(time => time > cutoff);
            this.threatDetection.suspiciousPatterns.set(key, recentPatterns);
            
            // Check for anomalies
            if (recentPatterns.length > this.threatDetection.anomalyThreshold) {
                this.handleSecurityAnomaly(type, details, recentPatterns.length);
            }
        } catch (error) {
            this.safeHandleError(error, 'security-pattern-analysis');
        }
    }
    
    /**
     * Handle detected security anomalies
     */
    handleSecurityAnomaly(type, details, patternCount) {
        const anomaly = {
            type: 'security_anomaly',
            originalType: type,
            patternCount,
            details,
            timestamp: Date.now()
        };
        
        this.logSecurityEvent('security_anomaly', anomaly);
        
        // Implement automatic response for severe anomalies
        if (patternCount > this.threatDetection.anomalyThreshold * 2) {
            this.triggerSecurityLockdown(type, details);
        }
    }
    
    /**
     * Trigger security lockdown for severe threats
     */
    triggerSecurityLockdown(type, details) {
        console.error('Security', '[SECURITY LOCKDOWN] Severe threat detected:', { type, details });
        
        // Could implement:
        // - Temporary disable of certain features
        // - Forced re-authentication
        // - Alert to security team
        // - Data protection measures
        
        this.logSecurityEvent('security_lockdown', {
            trigger: type,
            details,
            timestamp: Date.now()
        });
    }

    /**
     * Clean up old security events
     */
    cleanupSecurityEvents() {
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        this.securityEvents = this.securityEvents.filter(event => 
            new Date(event.timestamp).getTime() > oneHourAgo
        );
    }

    /**
     * Mask sensitive data for logging
     */
    maskSensitiveData(data, type) {
        if (!data || typeof data !== 'string') {
            return data;
        }

        switch (type) {
            case 'password':
                return '*'.repeat(Math.min(data.length, 8));
            case 'email':
                const [local, domain] = data.split('@');
                return local ? `${local.charAt(0)}***@${domain}` : data;
            case 'licenseKey':
                return data.length > 4 ? `${data.substring(0, 4)}****` : '****';
            default:
                return data.length > 10 ? `${data.substring(0, 6)}...` : data;
        }
    }

    /**
     * Get security status
     */
    getSecurityStatus() {
        return {
            cspEnabled: !!document.querySelector('meta[http-equiv="Content-Security-Policy"]'),
            recentEvents: this.securityEvents.slice(-10),
            lockedAccounts: Array.from(this.loginAttempts.keys()).filter(key => this.isAccountLocked(key)),
            totalEvents: this.securityEvents.length,
            policies: this.securityPolicies
        };
    }

    /**
     * Update security policy
     */
    updateSecurityPolicy(policy, value) {
        if (this.securityPolicies.hasOwnProperty(policy)) {
            this.securityPolicies[policy] = value;
            this.logSecurityEvent('policy_updated', { policy, value });
            console.log('Security', `[SecurityManager] Security policy updated: ${policy}`, );
        }
    }

    /**
     * Cleanup security manager resources
     */
    cleanup() {
        if (this.domObserver) {
            this.domObserver.disconnect();
            this.domObserver = null;
        }
    }
}

// Create singleton instance
const securityManager = new SecurityManager();

// Make available globally for browser environment
if (typeof window !== 'undefined') {
    window.SecurityManager = SecurityManager;
    window.securityManager = securityManager;
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SecurityManager,
        securityManager
    };
}

console.log('Security', '[SecurityManager] Enhanced security system initialized successfully');