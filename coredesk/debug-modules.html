<!DOCTYPE html>
<html>
<head>
    <title>Debug Module Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>CoreDesk Module Loading Debug</h1>
    <div id="results"></div>

    <script>
        // Simulate CoreDeskAuth environment
        window.CoreDeskAuth = {
            environment: 'production',
            api: {
                moduleRepositoryUrl: null
            }
        };

        // Simulate ModuleDownloader
        class TestModuleDownloader {
            constructor() {
                this.config = {
                    baseUrl: this.getDefaultBaseUrl()
                };
            }

            getDefaultBaseUrl() {
                const environment = window.CoreDeskAuth?.environment || 'production';
                if (environment === 'development') {
                    return 'http://localhost:3000/api/modules';
                } else {
                    return 'https://coredeskpro.com/api/modules';
                }
            }

            async fetchAvailableModules(filters = {}) {
                const url = this.config.baseUrl;
                const queryParams = new URLSearchParams();
                
                Object.entries(filters).forEach(([key, value]) => {
                    if (value) queryParams.append(key, value);
                });
                
                const fullUrl = queryParams.toString() ? `${url}?${queryParams}` : url;
                
                try {
                    const response = await fetch(fullUrl);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    const data = await response.json();
                    return data.modules || [];
                } catch (error) {
                    console.error('Failed to fetch available modules:', error);
                    return [];
                }
            }
        }

        function addResult(type, title, content) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<h3>${title}</h3><div>${content}</div>`;
            results.appendChild(div);
        }

        async function runTests() {
            addResult('info', 'Starting Tests', 'Testing module loading functionality...');

            // Test 1: Environment Detection
            const environment = window.CoreDeskAuth?.environment || 'production';
            addResult('info', 'Environment Detection', `Environment: ${environment}`);

            // Test 2: URL Generation
            const downloader = new TestModuleDownloader();
            const url = downloader.config.baseUrl;
            addResult('info', 'URL Generation', `Base URL: ${url}`);

            // Test 3: Fetch modules
            try {
                addResult('info', 'Fetching Modules', 'Attempting to fetch modules from server...');
                const modules = await downloader.fetchAvailableModules();
                addResult('success', 'Module Fetch Success', `
                    <p>Found ${modules.length} modules:</p>
                    <pre>${JSON.stringify(modules, null, 2)}</pre>
                `);
            } catch (error) {
                addResult('error', 'Module Fetch Failed', `
                    <p>Error: ${error.message}</p>
                    <pre>${error.stack}</pre>
                `);
            }

            // Test 4: Direct fetch test
            try {
                addResult('info', 'Direct Fetch Test', 'Testing direct fetch to API...');
                const response = await fetch('https://coredeskpro.com/api/modules');
                const data = await response.json();
                addResult('success', 'Direct Fetch Success', `
                    <p>Response status: ${response.status}</p>
                    <p>Success: ${data.success}</p>
                    <p>Modules count: ${data.modules?.length || 0}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `);
            } catch (error) {
                addResult('error', 'Direct Fetch Failed', `
                    <p>Error: ${error.message}</p>
                    <pre>${error.stack}</pre>
                `);
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>