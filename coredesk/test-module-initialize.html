<!DOCTYPE html>
<html>
<head>
    <title>ModulePackage Initialize Test</title>
</head>
<body>
    <h1>ModulePackage Initialize Test</h1>
    <div id="output"></div>
    
    <script>
        // Mock the necessary globals that ModulePackage expects
        window.GlobalLogger = {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
        
        function log(message) {
            console.log(message);
            const output = document.getElementById('output');
            output.innerHTML += message + '<br>';
        }
        
        // Load the ModulePackage class
        const script = document.createElement('script');
        script.src = 'src/js/core/ModulePackage.js';
        script.onload = function() {
            runTests();
        };
        document.head.appendChild(script);
        
        async function runTests() {
            log('🧪 Testing ModulePackage initialize method...');
            log('=========================================');
            
            try {
                log('✅ Test 1: Creating ModulePackage instance');
                
                const packageData = {
                    manifest: {
                        id: 'lexflow',
                        name: 'LexFlow',
                        version: '1.0.0',
                        description: 'Module LexFlow',
                        main: 'index.js',
                        author: 'Portal',
                        category: 'general'
                    },
                    moduleCode: `// Binary Module: lexflow\nconsole.log('Binary module lexflow installed successfully');`,
                    styles: '',
                    assets: {}
                };
                
                const modulePackage = new ModulePackage(packageData, {
                    validateManifest: true,
                    strictMode: false,
                    allowUnsafeCode: true
                });
                
                log('✅ Module package created successfully!');
                log(`   - ID: ${modulePackage.id}`);
                log(`   - Name: ${modulePackage.name}`);
                log(`   - Version: ${modulePackage.version}`);
                log(`   - isInitialized: ${modulePackage.isInitialized}`);
                
                // Test the validateManifest method
                log('✅ Test 2: Testing validateManifest method');
                modulePackage.validateManifest();
                log('✅ validateManifest() executed successfully!');
                
                // Test the initialize method
                log('✅ Test 3: Testing initialize method');
                await modulePackage.initialize();
                log('✅ initialize() executed successfully!');
                log(`   - isInitialized: ${modulePackage.isInitialized}`);
                log(`   - initializedAt: ${modulePackage.initializedAt}`);
                
                // Test getModuleClass method
                log('✅ Test 4: Testing getModuleClass method');
                const moduleClass = modulePackage.getModuleClass();
                log('✅ getModuleClass() executed successfully!');
                log(`   - moduleClass exists: ${typeof moduleClass === 'function'}`);
                
                log('🎉 All tests passed! The initialize method is working correctly.');
                
            } catch (error) {
                log('❌ Test failed: ' + error.message);
                console.error('Full error:', error);
            }
        }
    </script>
</body>
</html>