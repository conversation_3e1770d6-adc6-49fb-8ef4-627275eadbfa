<!DOCTYPE html>
<html>
<head>
    <title>ModulePackage Validation Test</title>
</head>
<body>
    <h1>ModulePackage Validation Test</h1>
    <div id="output"></div>
    
    <script>
        // Mock the necessary globals that ModulePackage expects
        window.GlobalLogger = {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
        
        function log(message) {
            console.log(message);
            const output = document.getElementById('output');
            output.innerHTML += message + '<br>';
        }
        
        // Load the ModulePackage class
        const script = document.createElement('script');
        script.src = 'src/js/core/ModulePackage.js';
        script.onload = function() {
            runTests();
        };
        document.head.appendChild(script);
        
        function runTests() {
            log('🧪 Testing ModulePackage validation fix...');
            log('=====================================');
            
            // Test 1: Create a valid module package like we do in app.js
            try {
                log('✅ Test 1: Creating valid binary module package');
                
                const packageData = {
                    manifest: {
                        id: 'lexflow',
                        name: 'LexFlow',
                        version: '1.0.0',
                        description: 'Module LexFlow',
                        main: 'index.js',
                        author: 'Portal',
                        category: 'general'
                    },
                    moduleCode: `// Binary Module: lexflow (19249 bytes)\n// Downloaded from server\nconsole.log('Binary module lexflow installed successfully');`,
                    styles: '',
                    assets: {}
                };
                
                const modulePackage = new ModulePackage(packageData, {
                    validateManifest: true,
                    strictMode: false,
                    allowUnsafeCode: true
                });
                
                log('✅ Module package created successfully!');
                log(`   - ID: ${modulePackage.id}`);
                log(`   - Name: ${modulePackage.name}`);
                log(`   - Version: ${modulePackage.version}`);
                
                // Test the validateManifest method specifically
                log('✅ Test 2: Testing validateManifest method');
                modulePackage.validateManifest();
                log('✅ validateManifest() executed successfully!');
                
            } catch (error) {
                log('❌ Test failed: ' + error.message);
                return;
            }
            
            // Test 2: Create an invalid module package to test validation
            try {
                log('✅ Test 3: Testing validation with invalid package');
                
                const invalidPackageData = {
                    manifest: {
                        // Missing required fields
                        name: 'InvalidModule',
                        version: '1.0.0'
                    },
                    moduleCode: '',
                    styles: '',
                    assets: {}
                };
                
                const invalidPackage = new ModulePackage(invalidPackageData, {
                    validateManifest: true
                });
                
                log('❌ This should have failed but didn\'t');
                
            } catch (error) {
                log('✅ Validation correctly caught invalid package: ' + error.message);
            }
            
            log('🎉 All tests passed! The ModulePackage validation fix is working.');
            log('The "validateManifest is not a function" error should now be resolved.');
        }
    </script>
</body>
</html>