{"appId": "com.coredesk.framework", "productName": "CoreDesk Framework", "directories": {"output": "dist"}, "files": ["src/**/*", "!src/js/packages/**/*", "!src/js/modules/**/*", "main.js", "preload.js", "package.json"], "asarUnpack": ["src/assets/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "src/assets/icons/app.ico", "publisherName": "CoreDesk Team", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "installerIcon": "src/assets/icons/app.ico", "uninstallerIcon": "src/assets/icons/app.ico"}, "buildDependenciesFromSource": false, "nodeGypRebuild": false, "compression": "normal"}