#!/usr/bin/env node

/**
 * CoreDesk Windows Build Script
 * Automated build for Windows testing in WSL environment
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 CoreDesk Windows Build Script');
console.log('=================================');

// Check if electron-builder is available
try {
    execSync('npx electron-builder --version', { stdio: 'pipe' });
    console.log('✅ electron-builder found');
} catch (error) {
    console.log('❌ electron-builder not found, installing...');
    execSync('npm install electron-builder --save-dev', { stdio: 'inherit' });
}

// Ensure dist directory exists
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

console.log('📦 Building Windows installer...');

try {
    // Clean previous builds
    console.log('🧹 Cleaning previous builds...');
    const distPath = path.join(__dirname, 'dist');
    if (fs.existsSync(distPath)) {
        fs.rmSync(distPath, { recursive: true, force: true });
    }
    
    // Build for Windows with specific configuration
    console.log('🔧 Building with asset optimization...');
    execSync('npx electron-builder --win --x64 --config electron-builder-fix.json', { 
        stdio: 'inherit',
        cwd: __dirname 
    });
    
    console.log('✅ Build completed successfully!');
    
    // List output files
    const files = fs.readdirSync(distDir);
    console.log('📄 Built files:');
    files.forEach(file => {
        const stats = fs.statSync(path.join(distDir, file));
        const size = (stats.size / 1024 / 1024).toFixed(2);
        console.log(`  - ${file} (${size} MB)`);
    });
    
    console.log('');
    console.log('🎯 Next steps:');
    console.log('1. Copy the .exe file to your Windows machine');
    console.log('2. Install and test module functionality');
    console.log('3. Modules will be loaded from https://coredeskpro.com');
    
} catch (error) {
    console.log('❌ Build failed:', error.message);
    console.log('💡 Try running: npm install && npm run build:win');
    process.exit(1);
}