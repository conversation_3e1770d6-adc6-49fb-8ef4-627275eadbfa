#!/usr/bin/env node

/**
 * Test script to verify ModulePackage validation is working
 * This tests the fix for the "validateManifest is not a function" error
 */

// Mock the necessary globals that ModulePackage expects
global.window = {
    GlobalLogger: {
        info: (tag, message) => console.log(`[${tag}] ${message}`),
        error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
        warn: (tag, message) => console.warn(`[${tag}] ${message}`),
        debug: (tag, message) => console.debug(`[${tag}] ${message}`)
    }
};

// Load the ModulePackage class
const fs = require('fs');
const path = require('path');
const modulePackagePath = path.join(__dirname, 'src/js/core/ModulePackage.js');
const modulePackageCode = fs.readFileSync(modulePackagePath, 'utf8');

// Remove the class keyword and eval it to create the class
const classCode = modulePackageCode.replace(/^class ModulePackage/, 'function ModulePackage');
eval(classCode);

console.log('🧪 Testing ModulePackage validation fix...');
console.log('=====================================');

// Test 1: Create a valid module package like we do in app.js
try {
    console.log('\n✅ Test 1: Creating valid binary module package');
    
    const packageData = {
        manifest: {
            id: 'lexflow',
            name: 'LexFlow',
            version: '1.0.0',
            description: 'Module LexFlow',
            main: 'index.js',
            author: 'Portal',
            category: 'general'
        },
        moduleCode: `// Binary Module: lexflow (19249 bytes)\n// Downloaded from server\nconsole.log('Binary module lexflow installed successfully');`,
        styles: '',
        assets: {}
    };
    
    const modulePackage = new ModulePackage(packageData, {
        validateManifest: true,
        strictMode: false,
        allowUnsafeCode: true
    });
    
    console.log('✅ Module package created successfully!');
    console.log(`   - ID: ${modulePackage.id}`);
    console.log(`   - Name: ${modulePackage.name}`);
    console.log(`   - Version: ${modulePackage.version}`);
    
    // Test the validateManifest method specifically
    console.log('\n✅ Test 2: Testing validateManifest method');
    modulePackage.validateManifest();
    console.log('✅ validateManifest() executed successfully!');
    
} catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
}

// Test 2: Create an invalid module package to test validation
try {
    console.log('\n✅ Test 3: Testing validation with invalid package');
    
    const invalidPackageData = {
        manifest: {
            // Missing required fields
            name: 'InvalidModule',
            version: '1.0.0'
        },
        moduleCode: '',
        styles: '',
        assets: {}
    };
    
    const invalidPackage = new ModulePackage(invalidPackageData, {
        validateManifest: true
    });
    
    console.log('❌ This should have failed but didn\'t');
    
} catch (error) {
    console.log('✅ Validation correctly caught invalid package:', error.message);
}

console.log('\n🎉 All tests passed! The ModulePackage validation fix is working.');
console.log('The "validateManifest is not a function" error should now be resolved.');