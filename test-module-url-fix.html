<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoreDesk - Module URL Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #004d40; color: #4caf50; }
        .error { background: #4d0000; color: #f44336; }
        .info { background: #003d7a; color: #2196f3; }
        .warning { background: #4d3d00; color: #ff9800; }
        button {
            background: #8b7dd8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #9a8ce0;
        }
        pre {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            color: #ffffff;
            border: 1px solid #404040;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .online { background: #4caf50; }
        .offline { background: #f44336; }
        .testing { background: #ff9800; }
    </style>
</head>
<body>
    <h1>🧪 CoreDesk Module URL Fix Test</h1>
    <p>Este archivo prueba la configuración de URL para la carga de módulos después de la corrección.</p>

    <div class="test-section">
        <h2>🔍 Información del Entorno</h2>
        <div id="environment-info">
            <p><strong>Hostname:</strong> <span id="hostname"></span></p>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>Es Electron:</strong> <span id="is-electron"></span></p>
            <p><strong>Es Localhost:</strong> <span id="is-localhost"></span></p>
            <p><strong>URL Configurada:</strong> <span id="configured-url"></span></p>
        </div>
    </div>

    <div class="test-section">
        <h2>🌐 Prueba de Conectividad</h2>
        <div id="connectivity-test">
            <button onclick="testConnectivity()">🔄 Probar Conexión</button>
            <div id="connectivity-results"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>📦 Prueba de Módulos</h2>
        <div id="modules-test">
            <button onclick="testModulesAPI()">📦 Probar API de Módulos</button>
            <div id="modules-results"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Simulación de CoreDesk</h2>
        <div id="coredesk-simulation">
            <button onclick="simulateCoreDeskModuleLoad()">🚀 Simular Carga de Módulos</button>
            <div id="simulation-results"></div>
        </div>
    </div>

    <script>
        // Función para obtener la URL del repositorio (copiada del app.js)
        function getModuleRepositoryUrl() {
            // Check if there's a configured URL in CoreDeskAuth
            if (window.CoreDeskAuth?.api?.moduleRepositoryUrl) {
                return window.CoreDeskAuth.api.moduleRepositoryUrl;
            }
            
            // For now, always use production URL since local development server may not be running
            // Users can set window.CoreDeskAuth.api.moduleRepositoryUrl if they have a local setup
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const isElectron = navigator.userAgent.toLowerCase().indexOf(' electron/') > -1;
            
            // Check for environment-based configuration
            const environment = window.CoreDeskAuth?.environment;
            
            if (environment === 'development' && isLocalhost && !isElectron) {
                // Only use localhost in web development when explicitly set to development
                return 'http://localhost:3000/api/modules';
            } else {
                // Use production URL for Electron apps and normal usage
                return 'https://coredeskpro.com/api/modules';
            }
        }

        function showResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<span class="status-indicator ${type === 'success' ? 'online' : type === 'error' ? 'offline' : 'testing'}"></span>${message}`;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
        }

        // Cargar información del entorno
        document.addEventListener('DOMContentLoaded', function() {
            const isElectron = navigator.userAgent.toLowerCase().indexOf(' electron/') > -1;
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const configuredUrl = getModuleRepositoryUrl();

            document.getElementById('hostname').textContent = window.location.hostname;
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('is-electron').textContent = isElectron ? 'Sí' : 'No';
            document.getElementById('is-localhost').textContent = isLocalhost ? 'Sí' : 'No';
            document.getElementById('configured-url').textContent = configuredUrl;
        });

        async function testConnectivity() {
            clearResults('connectivity-results');
            showResult('connectivity-results', 'info', 'Iniciando prueba de conectividad...');

            const testUrls = [
                'https://coredeskpro.com',
                'https://coredeskpro.com/api/modules',
                'http://localhost:3000/api/modules'
            ];

            for (const url of testUrls) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000);

                    const response = await fetch(url, {
                        method: 'GET',
                        mode: 'cors',
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (response.ok) {
                        showResult('connectivity-results', 'success', `✅ ${url} - Conectado (${response.status})`);
                    } else {
                        showResult('connectivity-results', 'warning', `⚠️ ${url} - ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    if (error.name === 'AbortError') {
                        showResult('connectivity-results', 'error', `❌ ${url} - Timeout (5s)`);
                    } else {
                        showResult('connectivity-results', 'error', `❌ ${url} - ${error.message}`);
                    }
                }
            }
        }

        async function testModulesAPI() {
            clearResults('modules-results');
            const moduleUrl = getModuleRepositoryUrl();
            
            showResult('modules-results', 'info', `📡 Probando API de módulos: ${moduleUrl}`);

            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);

                const response = await fetch(moduleUrl, {
                    method: 'GET',
                    mode: 'cors',
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    const data = await response.json();
                    showResult('modules-results', 'success', `✅ API disponible - Status: ${response.status}`);
                    
                    if (data.success && data.modules) {
                        showResult('modules-results', 'success', `📦 Módulos encontrados: ${data.modules.length}`);
                        
                        // Mostrar información de los módulos
                        const modulesList = data.modules.map(module => 
                            `• ${module.name} (${module.id}) - v${module.version}`
                        ).join('\n');
                        
                        const pre = document.createElement('pre');
                        pre.textContent = modulesList;
                        document.getElementById('modules-results').appendChild(pre);
                    } else {
                        showResult('modules-results', 'warning', `⚠️ Respuesta inválida: ${JSON.stringify(data)}`);
                    }
                } else {
                    showResult('modules-results', 'error', `❌ Error HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    showResult('modules-results', 'error', `❌ Timeout (10s) al conectar con ${moduleUrl}`);
                } else {
                    showResult('modules-results', 'error', `❌ Error de conexión: ${error.message}`);
                }
            }
        }

        async function simulateCoreDeskModuleLoad() {
            clearResults('simulation-results');
            showResult('simulation-results', 'info', '🚀 Simulando carga de módulos de CoreDesk...');

            // Simular la lógica de app.js
            const moduleUrl = getModuleRepositoryUrl();
            showResult('simulation-results', 'info', `📡 URL configurada: ${moduleUrl}`);

            try {
                // Log de debug (como en app.js)
                console.log('[CoreDeskApp] Debug: Attempting to fetch modules from:', moduleUrl);
                console.log('[CoreDeskApp] Debug: User agent:', navigator.userAgent);
                console.log('[CoreDeskApp] Debug: Location hostname:', window.location.hostname);

                const response = await fetch(moduleUrl);
                console.log('[CoreDeskApp] Debug: Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('[CoreDeskApp] Debug: API response:', data);

                if (!data.success || !data.modules) {
                    throw new Error('Invalid API response');
                }

                const modules = data.modules;
                console.log('[CoreDeskApp] Debug: Modules loaded:', modules);

                showResult('simulation-results', 'success', `✅ Simulación exitosa - ${modules.length} módulos cargados`);
                
                // Mostrar módulos simulados
                const moduleCards = modules.map(module => {
                    const isInstalled = false; // Simulado
                    const isDownloadable = module.downloadUrl && module.downloadUrl.trim() !== '';
                    
                    let statusText, statusClass;
                    if (isInstalled) {
                        statusText = 'Instalado';
                        statusClass = 'status-installed';
                    } else if (isDownloadable) {
                        statusText = 'Disponible';
                        statusClass = 'status-available';
                    } else {
                        statusText = 'No disponible';
                        statusClass = 'status-unavailable';
                    }
                    
                    return `
                        <div style="border: 1px solid #404040; padding: 15px; margin: 10px 0; border-radius: 8px;">
                            <h4>${module.name}</h4>
                            <p>${module.description}</p>
                            <small>Versión: ${module.version || 'N/A'}</small><br>
                            <small>Estado: <span class="${statusClass}">${statusText}</span></small>
                        </div>
                    `;
                }).join('');

                const container = document.getElementById('simulation-results');
                container.innerHTML += `<div>${moduleCards}</div>`;

            } catch (error) {
                showResult('simulation-results', 'error', `❌ Error en simulación: ${error.message}`);
                console.error('[CoreDeskApp] Simulation error:', error);
            }
        }

        // Auto-ejecutar pruebas al cargar
        setTimeout(() => {
            testConnectivity();
            testModulesAPI();
        }, 1000);
    </script>
</body>
</html>