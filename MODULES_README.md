# CoreDesk Module System

This document explains the complete module system architecture and workflow for CoreDesk v2.0.

## Overview

The CoreDesk module system allows for dynamic installation and management of feature modules. Modules are distributed through the portal server and can be downloaded and installed at runtime.

## Architecture

### Components

1. **CoreDesk Application** (`/coredesk/`) - Electron app that consumes modules
2. **Portal Server** (`/portal/`) - Distributes modules and serves download API
3. **Module Packages** (`/portal/downloads/modules/`) - Packaged module files
4. **Packaging Scripts** (`/scripts/`) - Build and deployment automation

### Module Structure

Each module consists of:
- **Module Implementation** (`/coredesk/src/js/modules/{name}/`) - Core JavaScript files
- **Package Wrapper** (`/coredesk/src/js/packages/{name}/`) - Package metadata and wrapper
- **Styles** (`/coredesk/src/css/modules/{name}.css`) - Module-specific CSS
- **Manifest** (`manifest.json`) - Module metadata and compatibility info
- **Package File** (`{name}-{version}.tar.gz`) - Compressed module package

## Available Modules

### Production Ready
- **LexFlow** (v0.0.2) - Legal case management system
- **ProtocolX** (v0.0.2) - Administrative process automation

### In Development  
- **AuditPro** (v0.0.1) - Audit and internal control system
- **FinSync** (v0.0.1) - Accounting and financial management

## Workflow

### 1. Module Development
1. Create module implementation in `/coredesk/src/js/modules/{name}/`
2. Add CSS styles in `/coredesk/src/css/modules/{name}.css`
3. Create package wrapper in `/coredesk/src/js/packages/{name}/`

### 2. Module Packaging
```bash
# Package all modules
npm run package-modules

# Or run directly
node scripts/package-modules.js
```

This creates:
- `/portal/downloads/modules/{name}/{version}/manifest.json`
- `/portal/downloads/modules/{name}/{version}/{name}-{version}.tar.gz`

### 3. Testing Locally
```bash
# Start portal server
cd portal && npm run dev

# Test API endpoint
curl http://localhost:3000/api/modules

# Test module download
curl http://localhost:3000/api/modules/{name}/download
```

### 4. Production Deployment
```bash
# Deploy to production server
npm run deploy-modules

# Or use the interactive script
npm run build-and-deploy
```

## API Endpoints

### Portal Server (`http://localhost:3000` or `https://coredeskpro.com`)

- `GET /api/modules` - List all available modules
- `GET /api/modules/{id}` - Get specific module info
- `GET /api/modules/{id}/download` - Download module package

### Response Format
```json
{
  "success": true,
  "modules": [
    {
      "id": "lexflow",
      "name": "LexFlow", 
      "version": "0.0.2",
      "status": "available",
      "downloadable": true,
      "size": "18.8 KB",
      "downloadUrl": "/api/modules/lexflow/download",
      "compatibility": {
        "coredesk": ">=0.0.2",
        "platforms": ["windows", "macos", "linux"]
      }
    }
  ]
}
```

## Configuration

### Module Configuration (`/portal/src/config/index.js`)
```javascript
modules: {
  lexflow: {
    name: 'LexFlow',
    description: 'Legal case management',
    features: [...]
  }
}
```

### ModuleDownloader Configuration (`/coredesk/src/js/services/ModuleDownloader.js`)
```javascript
baseUrl: process.env.NODE_ENV === 'production' 
  ? 'https://coredeskpro.com/api/modules/' 
  : 'http://localhost:3000/api/modules/'
```

## Production Server Structure

```
/opt/coredesk/data/downloads/modules/
├── lexflow/
│   └── 0.0.2/
│       ├── manifest.json
│       └── lexflow-0.0.2.tar.gz
├── protocolx/
│   └── 0.0.2/
│       ├── manifest.json  
│       └── protocolx-0.0.2.tar.gz
├── auditpro/
│   └── 0.0.1/
│       ├── manifest.json
│       └── auditpro-0.0.1.tar.gz
└── finsync/
    └── 0.0.1/
        ├── manifest.json
        └── finsync-0.0.1.tar.gz
```

## Troubleshooting

### "No hay módulos disponibles"
1. Check portal server is running: `curl http://localhost:3000/api/modules`
2. Verify module packages exist: `ls portal/downloads/modules/*/`
3. Check ModuleDownloader URL configuration
4. Verify portal moduleService can read manifest files

### Module Download Fails
1. Check download endpoint: `curl -I http://localhost:3000/api/modules/{name}/download`
2. Verify .tar.gz files exist and are readable
3. Check file permissions and checksums

### Development vs Production URLs
- Development: `http://localhost:3000/api/modules/`
- Production: `https://coredeskpro.com/api/modules/`

## Scripts Reference

- `npm run package-modules` - Package all modules
- `npm run deploy-modules` - Deploy to production server  
- `npm run build-and-deploy` - Package and deploy in one step
- `./scripts/package-modules.js` - Direct packaging script
- `./scripts/deploy-modules.sh` - Direct deployment script

## Security Notes

- All modules require license validation
- Checksums are verified during download
- Module packages are served over HTTPS in production
- Authentication required for module downloads in production