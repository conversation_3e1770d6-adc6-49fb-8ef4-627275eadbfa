#!/bin/bash

# CoreDesk Windows Build Script
# Builds the Windows installer for testing

echo "🚀 CoreDesk Windows Build Script"
echo "================================="

# Check if we're in WSL
if grep -qi wsl /proc/version; then
    echo "✅ Detected WSL environment"
else
    echo "⚠️  Not in WSL, but proceeding..."
fi

# Navigate to CoreDesk directory
cd /home/<USER>/coredesk/coredesk

echo "📦 Installing dependencies..."
npm install

echo "🔧 Building Windows installer..."
npm run build:win

if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Output files in: /home/<USER>/coredesk/coredesk/dist/"
    
    # List the built files
    echo "📄 Built files:"
    ls -la dist/
    
    echo ""
    echo "🎯 Installation instructions:"
    echo "1. Copy the .exe file from dist/ folder to your Windows machine"
    echo "2. Run the installer on Windows to test module installation"
    echo "3. The app will connect to https://coredeskpro.com for modules"
    
else
    echo "❌ Build failed!"
    echo "💡 Try: npm install && npm run build:win"
fi