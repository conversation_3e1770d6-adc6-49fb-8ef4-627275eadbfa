#!/bin/bash

# Guía de rutas comunes para subir archivos a CoreDesk

echo "📂 RUTAS COMUNES EN EL SERVIDOR COREDESK"
echo "========================================"
echo ""
echo "🏠 Directorio principal:"
echo "   /opt/coredesk/"
echo ""
echo "🐳 Docker exports (imágenes):"
echo "   /opt/coredesk/docker-exports/"
echo ""
echo "📊 Configuraciones:"
echo "   /opt/coredesk/docker-compose.yml"
echo "   /opt/coredesk/.env"
echo ""
echo "📁 Datos de aplicaciones:"
echo "   /opt/coredesk/data/api/"
echo "   /opt/coredesk/data/portal/"
echo "   /opt/coredesk/data/uploads/"
echo "   /opt/coredesk/data/downloads/"
echo ""
echo "📝 Logs:"
echo "   /opt/coredesk/logs/api/"
echo "   /opt/coredesk/logs/portal/"
echo "   /opt/coredesk/logs/admin-panel/"
echo ""
echo "💾 Backups:"
echo "   /opt/coredesk/backups/"
echo ""
echo "🔧 Scripts:"
echo "   /opt/coredesk/scripts/"
