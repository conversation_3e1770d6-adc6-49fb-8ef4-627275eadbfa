# Enhanced Generate Latest.yml Script

## 🚀 New Features Added

The script now includes **automatic upload functionality** with the following enhancements:

### ✅ **What's New**
1. **Auto Version Detection**: Automatically detects version from package.json or filenames
2. **Dynamic File Generation**: No more manual filename configuration
3. **Automatic Upload**: Upload files directly to production server
4. **Dry Run Mode**: See what would be uploaded without executing
5. **Progress Tracking**: Real-time upload status
6. **Auto Restart**: Automatically restart portal container
7. **Better Error Handling**: Clear error messages and fallbacks
8. **Help System**: Built-in usage guide

### 📋 **Usage Options**

```bash
# 1. Generate only (original behavior)
node generate-latest-yml.js

# 2. Generate and upload automatically
node generate-latest-yml.js --upload

# 3. Dry run - see what would be uploaded
node generate-latest-yml.js --upload --dry-run

# 4. Show help
node generate-latest-yml.js --help
```

### 🖥️ **Example Output**

#### Generate Only Mode:
```
🔧 Generating latest.yml with real file data...
📋 Mode: GENERATE ONLY

🔍 Auto-detecting version...
   ✅ Found version 0.0.2 in ./package.json
📋 Generated file list for version 0.0.2:
   ✅ vwindows: CoreDesk-0.0.2-win.exe
   ✅ vmacos: CoreDesk-0.0.2-mac.zip
   ✅ vlinux: CoreDesk-0.0.2-linux.AppImage

📁 Processing CoreDesk-0.0.2-win.exe...
   📏 Size: ~88 MB (92212002 bytes)
   🔒 Calculating SHA512 hash...
   ✅ SHA512: 47784dbdf0e6ba4d76a9...

✅ Generated latest.yml:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
version: 0.0.2
releaseDate: 2025-07-07T03:45:00.000Z
vwindows:
  url: CoreDesk-0.0.2-win.exe
  sha512: 47784dbdf0e6ba4d76a926062f2dff208cedf9f5883db970e54321b88622cbfc0d0cb1626da0ab005346b7fa926674ba2fcfc25dbbde3ed155670133a2248ba2
  size: 92212002
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💡 TIP: Use --upload flag to upload automatically:
   node generate-latest-yml.js --upload
```

#### Upload Mode:
```
🔧 Generating latest.yml with real file data...
📋 Mode: GENERATE + UPLOAD

[... generation output ...]

🚀 Starting upload process...

📤 Uploading 4 files to coredesk-prod:/opt/coredesk/data/downloads/app/...

📁 Uploading latest.yml...
   ✅ latest.yml uploaded successfully
📁 Uploading CoreDesk-0.0.2-win.exe...
   ✅ CoreDesk-0.0.2-win.exe uploaded successfully
📁 Uploading CoreDesk-0.0.2-mac.zip...
   ✅ CoreDesk-0.0.2-mac.zip uploaded successfully
📁 Uploading CoreDesk-0.0.2-linux.AppImage...
   ✅ CoreDesk-0.0.2-linux.AppImage uploaded successfully

🔄 Restarting portal container...
   ✅ Portal container restarted

🎉 Upload completed! The download page will show updated information.
🔗 Check: https://coredeskpro.com/download
```

#### Dry Run Mode:
```
🔧 Generating latest.yml with real file data...
📋 Mode: DRY RUN - Show upload commands

[... generation output ...]

🚀 Starting upload process...

📋 DRY RUN - Commands that would be executed:

1. Upload latest.yml:
   scp latest.yml coredesk-prod:/opt/coredesk/data/downloads/app/

2. Upload executables:
   scp CoreDesk-0.0.2-win.exe coredesk-prod:/opt/coredesk/data/downloads/app/
   scp CoreDesk-0.0.2-mac.zip coredesk-prod:/opt/coredesk/data/downloads/app/
   scp CoreDesk-0.0.2-linux.AppImage coredesk-prod:/opt/coredesk/data/downloads/app/

3. Restart portal container:
   ssh coredesk-prod "cd /opt/coredesk && docker-compose restart portal"
```

### ⚙️ **Configuration**

The script uses a configuration object that can be easily modified:

```javascript
const config = {
  production: {
    host: 'coredesk-prod',                    // SSH host
    path: '/opt/coredesk/data/downloads/app/', // Remote path
    user: 'administrator'                      // SSH user (optional)
  },
  // Version detection sources (in priority order)
  versionSources: [
    './package.json',           // Check local package.json
    '../package.json',          // Check parent directory
    '../coredesk/package.json', // Check coredesk directory
    '../portal/package.json'    // Check portal directory
  ],
  // File patterns - automatically filled with detected version
  filePatterns: {
    'vwindows': 'CoreDesk-{VERSION}-win.exe',
    'vmacos': 'CoreDesk-{VERSION}-mac.zip', 
    'vlinux': 'CoreDesk-{VERSION}-linux.AppImage'
  },
  // Fallback version if auto-detection fails
  fallbackVersion: '0.0.2'
};
```

### 🔧 **Requirements**

1. **SSH Access**: Configured SSH key access to production server
2. **SCP Command**: Available in your PATH
3. **File Structure**: Executables in the same directory as script
4. **Permissions**: Write access to production server path

### 🛡️ **Error Handling**

The script includes robust error handling:
- ✅ **Missing files**: Skips with warning, continues with others
- ✅ **Upload failures**: Stops on first failure, shows clear error
- ✅ **SSH issues**: Falls back to manual restart instructions
- ✅ **Permission errors**: Clear error messages

### 🚀 **Workflow Integration**

Perfect for automated workflows:

```bash
# Build executables
./build-coredesk.sh

# Generate and deploy
node apps/generate-latest-yml.js --upload

# The download page now shows updated info automatically!
```

### 🔗 **Benefits**

1. **Zero Configuration**: Automatically detects version from your project
2. **Smart File Detection**: Finds executables by pattern or existing files
3. **One Command Deploy**: From executables to live download page
4. **No Manual Steps**: Eliminates copy-paste errors and version mismatches
5. **Safe Testing**: Dry run mode for verification
6. **Automatic Restart**: Portal updates immediately
7. **Progress Feedback**: Know exactly what's happening

This transforms the deployment process from **multiple manual steps with version coordination** to **one simple command that handles everything automatically**! 🎉